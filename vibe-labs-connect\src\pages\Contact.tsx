
import { ArrowLeft, Mail, MessageCircle, Upload, Send } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

const Contact = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-white/90 backdrop-blur-md border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link to="/" className="flex items-center space-x-2 text-slate-600 hover:text-primary transition-colors">
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <div className="text-2xl font-bold text-slate-800">
              Contact Us
            </div>
            <div className="w-32"></div> {/* Spacer for layout balance */}
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-slate-800 mb-6">
            Talk to the Team.
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-500">
              Start a Project.
            </span>
          </h1>
          <p className="text-xl text-slate-600 mb-12">
            Ready to transform your idea into reality? Let's discuss how IssyLabs can help you succeed.
          </p>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Contact Info */}
            <div className="lg:col-span-1">
              <Card className="bg-white border-slate-200 shadow-lg">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-slate-800 mb-6">Get in Touch</h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start">
                      <Mail className="h-6 w-6 text-primary mr-3 mt-1" />
                      <div>
                        <p className="text-slate-800 font-medium">Email</p>
                        <p className="text-slate-600"><EMAIL></p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <MessageCircle className="h-6 w-6 text-primary mr-3 mt-1" />
                      <div>
                        <p className="text-slate-800 font-medium">Response Time</p>
                        <p className="text-slate-600">Within 24 hours</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                    <p className="text-slate-600 text-sm">
                      <strong className="text-slate-800">Quick tip:</strong> The more details you share about your project, the better we can tailor our response to your needs.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card className="bg-white border-slate-200 shadow-lg">
                <CardContent className="p-8">
                  <form className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-slate-800 font-medium mb-2">Name *</label>
                        <Input 
                          className="bg-slate-50 border-slate-200 text-slate-800 placeholder:text-slate-500" 
                          placeholder="Your full name"
                        />
                      </div>
                      <div>
                        <label className="block text-slate-800 font-medium mb-2">Email *</label>
                        <Input 
                          type="email" 
                          className="bg-slate-50 border-slate-200 text-slate-800 placeholder:text-slate-500"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-slate-800 font-medium mb-2">Industry/Niche</label>
                      <select className="w-full p-3 bg-slate-50 border border-slate-200 text-slate-800 rounded-md">
                        <option>Select your industry</option>
                        <option>Fashion</option>
                        <option>Real Estate</option>
                        <option>Coaching & Education</option>
                        <option>Music & Entertainment</option>
                        <option>Video Creation</option>
                        <option>E-commerce</option>
                        <option>Healthcare</option>
                        <option>Technology</option>
                        <option>Other</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-slate-800 font-medium mb-2">Service Type</label>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="issylabs" className="rounded bg-slate-50 border-slate-200" />
                          <label htmlFor="issylabs" className="text-slate-600">IssyLabs (Technical)</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="issyvibe" className="rounded bg-slate-50 border-slate-200" />
                          <label htmlFor="issyvibe" className="text-slate-600">IssyVibe (Creative)</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="both" className="rounded bg-slate-50 border-slate-200" />
                          <label htmlFor="both" className="text-slate-600">Both Services</label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" id="consultation" className="rounded bg-slate-50 border-slate-200" />
                          <label htmlFor="consultation" className="text-slate-600">Consultation Only</label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-slate-800 font-medium mb-2">Project Details *</label>
                      <Textarea 
                        className="bg-slate-50 border-slate-200 text-slate-800 placeholder:text-slate-500" 
                        rows={6}
                        placeholder="Tell us about your project goals, timeline, budget range, and any specific requirements..."
                      />
                    </div>

                    <div>
                      <label className="block text-slate-800 font-medium mb-2">File Upload (Optional)</label>
                      <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-slate-400 transition-colors cursor-pointer bg-slate-50">
                        <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                        <p className="text-slate-600 mb-1">Upload project files, references, or inspiration</p>
                        <p className="text-slate-500 text-sm">Max file size: 10MB</p>
                      </div>
                    </div>

                    <Button className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-4 text-lg shadow-lg">
                      Send Message
                      <Send className="ml-2 h-5 w-5" />
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-slate-800 text-center mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-6">
            <Card className="bg-slate-50 border-slate-200">
              <CardContent className="p-6">
                <h3 className="text-slate-800 font-semibold mb-2">How long does a typical project take?</h3>
                <p className="text-slate-600">Project timelines vary based on complexity. Simple landing pages take 1-2 weeks, while comprehensive systems with video production can take 4-8 weeks. We'll provide a detailed timeline during our initial consultation.</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-50 border-slate-200">
              <CardContent className="p-6">
                <h3 className="text-slate-800 font-semibold mb-2">Do you work with clients remotely?</h3>
                <p className="text-slate-600">Yes! We work with clients worldwide. All our communication, project management, and delivery processes are designed for seamless remote collaboration.</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-50 border-slate-200">
              <CardContent className="p-6">
                <h3 className="text-slate-800 font-semibold mb-2">Can I get just technical or just creative services?</h3>
                <p className="text-slate-600">Absolutely. While we specialize in integrated solutions, you can work with IssyLabs for technical services only or IssyVibe for creative production only, based on your needs.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
