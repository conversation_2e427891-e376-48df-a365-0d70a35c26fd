import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Eye, ChevronR<PERSON>, <PERSON>, Star } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ContactForm from "@/components/ContactForm";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import { FaWhatsapp } from "react-icons/fa";
import { HiOutlineMail } from "react-icons/hi";
import { title } from "process";


// Floating WhatsApp Button (same as home)
const FloatingButtonGroup = ({ section }) => {
  const phone = '12494336588';
  const message = encodeURIComponent(`Hello, I am reaching out to you from your Healthcare page. I would like to know more about your services.`);
  const whatsappUrl = `https://wa.me/${phone}?text=${message}`;
  return (
    <div className="fixed bottom-5 right-32 z-50 flex flex-col items-end gap-4">
      <a
        href={whatsappUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="rounded-full bg-green-500 hover:bg-green-600 shadow-lg w-14 h-14 flex items-center justify-center transition-all"
        aria-label="Chat on WhatsApp"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.967-.94 1.166-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.1 3.2 5.077 4.363.71.306 1.263.489 1.695.626.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/><path fill="currentColor" fillRule="evenodd" d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.34 4.997L2.003 22l5.137-1.343c1.462.8 3.09 1.222 4.864 1.222 5.514 0 9.997-4.483 9.997-9.997 0-2.668-1.04-5.175-2.927-7.062-1.888-1.888-4.395-2.927-7.062-2.927zm-8.001 9.997c0-4.418 3.583-8.001 8.001-8.001 2.137 0 4.146.832 5.656 2.343 1.511 1.51 2.343 3.519 2.343 5.656 0 4.418-3.583 8.001-8.001 8.001-1.548 0-3.04-.44-4.32-1.272l-.307-.19-3.057.799.82-2.995-.2-.314C3.44 15.04 3.003 13.548 3.003 12z" clipRule="evenodd"/></svg>
      </a>
    </div>
  );
};


const travelTechnicalProjects = [
  {
    title: "Travel Booking Platform",
    description: "End-to-end booking system for flights, hotels, and tours.",
    image: "photo-1522199873717-bc67b1a5e32b",
    liveUrl: "https://issylabs.wixsite.com/urbantravels",
    tags: ["Booking", "Flights", "Payments"],
  },
  {
    title: "Tour and Route Management System",
    description: "Personalized travel planning and itinerary management.",
    image: "photo-1569629743817-70d8db6c323b",
    liveUrl: "https://issylabs.wixsite.com/bonvoyage",
    tags: ["Itinerary", "Tourism", "Personalization"],
  },
  {
    title:"Explore Travel Blog Website",
    description: "Sample of a professional exploration travel agency website used as a UI/UX ",
    image: "photo-1619120238346-978e07731e77",
    liveUrl: "https://sunnylake.webflow.io/",
    tags: ["Blogging", "Content Management", "Social Media"],
  },
  {
      title: "IssyVibe Production (Our Patner)",
      description: "Visual storytelling powerhouse from cinematic brand films to social-ready reels. IssyVibe Production brings fashion, product, and lifestyle visions to life through world-class photography and video content designed to captivate and convert.",
      category: "technical",
      image: "photo-1655926478996-a65b83efe17c?fm",
      tags: ["Portfolio", "Booking System", "Responsive Design", "SEO"],
      liveUrl: "https://issyvibesproduction.vercel.app/"
    },
];

const travelCreativeProjects = [
  {
    title: "Destination Campaigns",
    description: "Visual storytelling for travel brands and agencies.",
    image: "photo-1529074963764-98f45c47344b",
    videoUrl: "-_4LC6y4ifo",
    tags: ["Photography", "Video", "Branding"],
  },
  {
    title: "Global Travel Showcase",
    description: "Highlighting diverse travel destinations and experiences.",
    image: "photo-1619120238346-978e07731e77",
    videoUrl: "o7poX65O5ng",
    tags: ["Destination", "Experiences", "Showcase"],
  },
  {
    title: "Travel Influencer Campaigns",
    description: "Collaborating with travel influencers to create engaging content.",
    image: "photo-1589542486639-a5ea813919b8",
    videoUrl: "R53OsP-pqA8",
    tags: ["Influencer", "Content", "Collaboration"],
  },
  {
    title: "IssyLabs Growth Media",
    description: "Visual storytelling powerhouse from cinematic brand films to social-ready reels. IssyLabs Growth Media brings travel, product, and lifestyle visions to life through world-class photography and video content designed to captivate and convert.",
    image: "photo-1630323826753-5de6ffa370d5",
    tags: ["Portfolio", "Booking System", "Responsive Design", "SEO"],
    videoUrl: "hUlpDdM-b4A",
  }
];

const travelTestimonials = [
  {
    name: "Linda T.",
    role: "Travel Agency Owner",
    content:
      "IssyLabs built our booking platform and created stunning destination campaigns. Our bookings and brand awareness soared!",
    image: "photo-1594744803329-e58b31de8bf5",
    rating: 5,
  },
  {
    name: "John D.",
    role: "Tour Operator",
    content:
      "IssyLabs' tour management system is a game-changer. It's intuitive and has saved us so much time.",
    image: "photo-1560250097-0b93528c311a",
    rating: 5,
  },
  {
    name: "Sarah M.",
    role: "Travel Blogger",   
    content:
      "IssyLabs' creative services have elevated our travel blog. Our audience loves the new look and feel.",
    image: "photo-1438761681033-6461ffad8d80",
    rating: 5,
  },
];

const businessTypes = [
  "Travel Agency",
  "Tour Operator",
  "Travel Blogger",
  "Hotel Manager",
  "Other",
];

const VideoModal = ({ videoId, thumbnail, title, description }) => (
  <Dialog>
    <DialogTrigger asChild>
      <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
        <img 
          src={`https://images.unsplash.com/${thumbnail}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`} 
          alt={title}
          className="w-full h-64 object-cover"
        />
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
          <div className="bg-primary/90 rounded-full p-4 glow-primary">
            <Play className="w-8 h-8 text-white fill-white" />
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <h3 className="text-white font-semibold text-lg">{title}</h3>
          {description && (
            <p className="text-white/80 text-xs mt-1">{description}</p>
          )}
        </div>
      </div>
    </DialogTrigger>
    <DialogContent className="max-w-4xl w-full bg-card border-border">
      <div className="aspect-video w-full">
        <iframe
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="w-full h-full rounded-lg"
        />
      </div>
    </DialogContent>
  </Dialog>
);

const Travel = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [activeSection, setActiveSection] = useState("home");
  const projects = [
    ...travelTechnicalProjects.map((p) => ({ ...p, category: "technical" })),
    ...travelCreativeProjects.map((p) => ({ ...p, category: "creative" })),
  ];
  const filteredProjects = activeTab === "all" ? projects : projects.filter(p => p.category === activeTab);

  return (
    <div className="min-h-screen premium-gradient-bg">
      <FloatingButtonGroup section={activeSection} />
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <MapPin className="h-6 w-6 text-accent mr-2" />
              IssyLabs Travels
            </div>
            <div className="hidden md:flex space-x-8">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' })}
                  className={`text-sm font-medium transition-all duration-300 hover:text-accent text-muted-foreground`}
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
            <Button
              className="md:hidden text-foreground"
              onClick={() => setActiveTab(activeTab === "menu" ? "all" : "menu")}
            >
              {activeTab === "menu" ? <Eye className="h-6 w-6" /> : <MapPin className="h-6 w-6" />}
            </Button>
            <Button 
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
            >
              Start Your Project
            </Button>
          </div>
          {activeTab === "menu" && (
            <div className="md:hidden py-4 border-t border-border glass-effect">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => { document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' }); setActiveTab("all"); }}
                  className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1523833082115-1e8e294bd14e?q=80&w=1990&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Where Travel<br />
              <span className="text-accent float-animation inline-block">Begins</span><br />
              and Experiences<br />
              <span className="text-primary float-animation inline-block" style={{animationDelay: '1s'}}>Unfold</span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
              Launch your travel business. Scale your bookings. Build something iconic with our unified technical and creative approach for travel professionals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Travel Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section - Travel Focused */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              One Team. Two Forces. Unified Travel Execution.
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
              IssyLabs combines technical excellence with creative storytelling to deliver complete digital solutions for travel brands.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12">
            {/* IssyLabs for Travel */}
            <div className="hover-lift glass-effect premium-shadow scale-in p-8 bg-card">
              <div className="flex items-center mb-6">
                <MapPin className="h-10 w-10 text-primary mr-4" />
                <div>
                  <h3 className="text-2xl font-bold text-foreground">Travel Tech Solutions</h3>
                  <p className="text-muted-foreground">IssyLabs for Travel</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-6">
                Your digital backbone for speed, structure, and growth in the travel industry.
              </p>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Booking platforms & itinerary management
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Analytics dashboards
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Custom travel web and mobile applications
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  CRM & customer relationship management
                </li>
              </ul>
            </div>
            {/* IssyVibe for Travel */}
            <div className="hover-lift glass-effect premium-shadow scale-in p-8 bg-card" style={{animationDelay: '0.2s'}}>
              <div className="flex items-center mb-6">
                <MapPin className="h-10 w-10 text-accent mr-4" />
                <div>
                  <h3 className="text-2xl font-bold text-foreground">Travel Visual Production</h3>
                  <p className="text-muted-foreground">IssyVibe for Travel</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-6">
                Creative services powered by our in-house partner bringing your travel brand to life through compelling visuals and content.
              </p>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Brand videos & destination showcase
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Travel product photography
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Social media content & reels
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Destination marketing campaigns
                </li>

              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Technical Solutions + Creative Media
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to launch, scale, and succeed in the travel industry.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Technical Services */}
            <div className="hover-lift glass-effect premium-shadow fade-in-left p-8 bg-card">
              <div className="flex items-center mb-6">
                <MapPin className="h-10 w-10 text-primary" />
                <span className="ml-4 text-xl font-bold text-foreground">Travel Tech</span>
              </div>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Booking platforms & itinerary management
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Analytics dashboards
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Custom travel web and mobile applications
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  CRM & customer relationship management
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  API integrations & data management for travel systems
                </li>
              </ul>
            </div>
            {/* Creative Services */}
            <div className="hover-lift glass-effect premium-shadow fade-in-right p-8 bg-card">
              <div className="flex items-center mb-6">
                <MapPin className="h-10 w-10 text-accent" />
                <span className="ml-4 text-xl font-bold text-foreground">Travel Media</span>
              </div>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Brand videos & destination showcase
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Travel product photography
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Social media content & reels
                </li>

                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Destination marketing campaigns
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Influencer & content partnerships
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Travel Projects We've Delivered
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns see how we deliver complete solutions for travel brands.
            </p>
          </div>
          {/* Technical Travel Projects */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <MapPin className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Travel Tech Solutions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {travelTechnicalProjects.map((project, index) => (
                <div 
                  key={index} 
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in bg-card"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => window.open(project.liveUrl, '_blank')}
                >
                  <div className="h-64 relative overflow-hidden rounded-t-lg"
                    style={{
                      backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                      {project.title}
                    </h4>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {project.description}
                    </p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.tags.map((tag, i) => (
                        <span key={i} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold">
                          {tag}
                        </span>
                      ))}
                    </div>
                    <Button 
                      size="sm" 
                      className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
                      onClick={() => window.open(project.liveUrl, 'https://www.intrepidtravel.com/en')}
                    >
                      View Project
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Creative Travel Projects */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <MapPin className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Travel Visual Productions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {travelCreativeProjects.map((project, index) => (
                <div key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in bg-card" style={{animationDelay: `${index * 0.1}s`}}>
                  <VideoModal
                    videoId={project.videoUrl}
                    thumbnail={project.image}
                    title={project.title}
                    description={project.description}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Travel Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from travel professionals we've helped transform their business.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {travelTestimonials.map((testimonial, index) => (
              <div key={index} className="glass-effect premium-shadow hover-lift scale-in bg-card" style={{animationDelay: `${index * 0.1}s`}}>
                <div className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section - Deep Blue Background Only */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{backgroundColor: '#0a1433'}}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">Ready to Transform Your Travel Brand?</h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{animationDelay: '0.2s'}}>Let's build the perfect tech and creative strategy for your travel business.</p>
          </div>
          <div className="fade-in-up" style={{animationDelay: '0.4s'}}>
            <ContactForm 
              pageSource="Travel Page"
              businessTypes={businessTypes}
            />
          </div>
        </div>
      </section>

      {/* Footer - Match Home Page Style */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <MapPin className="h-6 w-6 text-accent mr-2" />
                IssyLabs
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Travel Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner IssyVibe Production.
              </p>
              <br />
           <div className="space-y-4 text-3xl font-bold">
                              {/* Email */}
                              <a
                                href="mailto:<EMAIL>"
                                className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
                              >
                                <HiOutlineMail className="text-gray-500" />
                                <span><EMAIL></span>
                              </a>
                      
                              {/* WhatsApp */}
                              <a
                                href="https://wa.me/12494336588"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-3 text-green-600 hover:underline"
                              >
                                <FaWhatsapp className="text-green-500" />
                                <span>+1 (249) 433‑6588‬</span>
                              </a>
                            </div>
                    
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="/" className="text-muted-foreground hover:text-accent transition-colors">Home</a></li>
                <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li><a href="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</a></li>
                <li><a href="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</a></li>
                <li><a href="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</a></li>
                <li><a href="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</a></li>
                <li><a href="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</a></li>
                <li><a href="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</a></li>
                <li><a href="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</a></li>
                <li><a href="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Travel;
             