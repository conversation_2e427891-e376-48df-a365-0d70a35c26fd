
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ChevronRight, Mail, Calendar, Phone } from "lucide-react";
import { sendContactEmail, ContactFormData } from "@/services/emailService";

interface ContactFormProps {
  pageSource?: string;
  businessTypes?: string[];
}

const ContactForm = ({ pageSource = "Website", businessTypes }: ContactFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    business_type: '',
    service_type: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const emailData: ContactFormData = {
      ...formData,
      page_source: pageSource
    };

    const success = await sendContactEmail(emailData);
    
    if (success) {
      setShowSuccessMessage(true);
      setFormData({
        name: '',
        email: '',
        business_type: '',
        service_type: '',
        message: ''
      });
      setTimeout(() => setShowSuccessMessage(false), 5000);
    } else {
      alert('Failed to send message. Please try again or contact us <NAME_EMAIL>');
    }
    
    setIsSubmitting(false);
  };

  const defaultBusinessTypes = [
    "Fashion", "Real Estate", "Coaching & Education", "Music & Entertainment", 
    "Video Creation", "E-commerce", "Healthcare", "Technology", "Fitness & Gym", "Other"
  ];

  return (
    <div className="grid lg:grid-cols-3 gap-12">
      {/* Contact Info */}
      <div className="lg:col-span-1">
        <Card className="hover-lift glass-effect premium-shadow scale-in">
          <CardContent className="p-8">
            <h3 className="text-2xl font-bold text-foreground mb-6">Get in Touch</h3>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="p-2 bg-primary/20 rounded-full mr-3 mt-1">
                  <Mail className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-foreground font-medium">Email Us</p>
                  <p className="text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="p-2 bg-accent/20 rounded-full mr-3 mt-1">
                  <Calendar className="h-5 w-5 text-accent" />
                </div>
                <div>
                  <p className="text-foreground font-medium">Book a Call</p>
                  <p className="text-muted-foreground">Free consultation</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="p-2 bg-primary/20 rounded-full mr-3 mt-1">
                  <Phone className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-foreground font-medium">Response Time</p>
                  <p className="text-muted-foreground">Within 24 hours</p>
                </div>
              </div>
            </div>

            <Button 
              className="w-full mt-8 bg-accent hover:bg-accent/90 text-accent-foreground hover-lift pulse-glow"
              onClick={() => window.open('https://calendly.com/issylabs', '_blank')}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Book Consultation
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Contact Form */}
      <div className="lg:col-span-2">
        <Card className="hover-lift glass-effect premium-shadow scale-in" style={{animationDelay: '0.2s'}}>
          <CardContent className="p-8">
            {showSuccessMessage && (
              <div className="mb-8 p-4 bg-primary/10 border border-primary/20 rounded-lg text-primary text-center">
                Thank you! Your message has been sent successfully — we'll get back to you within 24 hours.
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-foreground font-medium mb-2">Name *</label>
                  <Input 
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground placeholder:text-muted-foreground" 
                    placeholder="Your full name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-foreground font-medium mb-2">Email *</label>
                  <Input 
                    type="email" 
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground placeholder:text-muted-foreground"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-foreground font-medium mb-2">Business Type</label>
                <select 
                  name="business_type"
                  value={formData.business_type}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground rounded-md"
                >
                  <option value="">Select your focus</option>
                  {(businessTypes || defaultBusinessTypes).map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-foreground font-medium mb-2">Service Type</label>
                <select 
                  name="service_type"
                  value={formData.service_type}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground rounded-md"
                >
                  <option value="">Select service needed</option>
                  <option value="IssyLabs (Technical)">IssyLabs (Technical)</option>
                  <option value="IssyVibe (Creative)">IssyVibe (Creative)</option>
                  <option value="Both Services">Both Services</option>
                  <option value="Consultation Only">Consultation Only</option>
                </select>
              </div>
              
              <div>
                <label className="block text-foreground font-medium mb-2">Project Details *</label>
                <Textarea 
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground placeholder:text-muted-foreground" 
                  rows={4}
                  placeholder="Tell us about your project, goals, timeline, and budget..."
                  required
                />
              </div>
              
              <Button 
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-accent hover:bg-accent/90 text-accent-foreground font-semibold py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                {isSubmitting ? 'Sending...' : 'Let\'s Build It Together'}
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ContactForm;
