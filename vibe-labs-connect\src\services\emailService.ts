
import emailjs from '@emailjs/browser';

// EmailJS configuration
const EMAILJS_SERVICE_ID = 'service_issylabs';
const EMAILJS_TEMPLATE_ID = 'template_contact';
const EMAILJS_PUBLIC_KEY = 'your_public_key_here';

export interface ContactFormData {
  name: string;
  email: string;
  business_type?: string;
  service_type?: string;
  message: string;
  page_source?: string;
}

export const sendContactEmail = async (formData: ContactFormData): Promise<boolean> => {
  try {
    console.log('Sending email with data:', formData);
    
    // Try EmailJS first
    try {
      const result = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID,
        {
          from_name: formData.name,
          from_email: formData.email,
          business_type: formData.business_type || 'Not specified',
          service_type: formData.service_type || 'Not specified',
          message: formData.message,
          page_source: formData.page_source || 'Website',
          to_email: '<EMAIL>',
        },
        EMAILJS_PUBLIC_KEY
      );
      
      console.log('EmailJS success:', result);
      return true;
    } catch (emailJsError) {
      console.log('EmailJS failed, trying Formspree fallback:', emailJsError);
      
      // Fallback to Formspree
      const formspreeResponse = await fetch('https://formspree.io/f/xldlrbne', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (formspreeResponse.ok) {
        console.log('Formspree success');
        return true;
      } else {
        throw new Error('Both EmailJS and Formspree failed');
      }
    }
  } catch (error) {
    console.error('Email service error:', error);
    return false;
  }
};
