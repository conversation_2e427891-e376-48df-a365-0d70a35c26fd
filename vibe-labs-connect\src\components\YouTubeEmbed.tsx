
import { useState } from 'react';
import { Play } from 'lucide-react';

interface YouTubeEmbedProps {
  videoId: string;
  title: string;
  className?: string;
}

const YouTubeEmbed = ({ videoId, title, className = "" }: YouTubeEmbedProps) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    setIsPlaying(true);
  };

  if (!isPlaying) {
    // Show thumbnail with play button
    return (
      <div className={`relative w-full cursor-pointer group ${className}`}>
        <img
          src={`https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`}
          alt={title}
          className="w-full h-full object-cover rounded-lg shadow-lg"
          onClick={handlePlay}
        />
        <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/50 transition-colors rounded-lg">
          <div className="bg-red-600 hover:bg-red-700 rounded-full p-4 transform group-hover:scale-110 transition-transform">
            <Play className="h-8 w-8 text-white ml-1" fill="white" />
          </div>
        </div>
      </div>
    );
  }

  // Show actual video when playing
  return (
    <div className={`relative w-full ${className}`}>
      <iframe
        width="100%"
        height="315"
        src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1&showinfo=0`}
        title={title}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
        className="rounded-lg shadow-lg"
        loading="lazy"
      />
    </div>
  );
};

export default YouTubeEmbed;
