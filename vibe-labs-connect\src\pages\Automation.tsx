import { ArrowLeft, ChevronRight, Code, Video, Settings, Zap, Database, BarChart3, Bot, Workflow, Cpu, Star, CheckCircle, Mail, Phone, Calendar, Play, Eye, Smartphone, ExternalLink } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useState } from "react";
import ContactForm from "@/components/ContactForm";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";

const Automation = () => {
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [activeTab, setActiveTab] = useState("all");

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 5000);
  };

  const automationTechnicalProjects = [
    {
      title: "AI-Powered Business Automation",
      description: "Complete workflow automation system with AI integration, reducing manual tasks by 85%",
      tags: ["AI Integration", "Workflow Automation", "Task Management", "Process Optimization"],
      image: "photo-1518770660439-4636190af475",
      liveUrl: "https://example-ai-automation.com"
    },
    {
      title: "Multi-Channel CRM System",
      description: "Unified customer management platform with automated lead scoring and nurturing workflows",
      tags: ["CRM Automation", "Lead Management", "Multi-Channel", "Analytics Dashboard"],
      image: "photo-1461749280684-dccba630e2f6",
      liveUrl: "https://example-crm-automation.com"
    },
    {
      title: "E-commerce Automation Suite",
      description: "Complete e-commerce automation including inventory, orders, and customer service bots",
      tags: ["E-commerce", "Inventory Automation", "Customer Service", "Order Processing"],
      image: "photo-1556742049-0cfed4f6a45d",
      liveUrl: "https://example-ecommerce-automation.com"
    },
    {
      title: "Data Analytics Automation",
      description: "Real-time data processing and automated reporting system with predictive analytics",
      tags: ["Data Processing", "Automated Reports", "Predictive Analytics", "Real-time Monitoring"],
      image: "photo-1551288049-bebda4e38f71",
      liveUrl: "https://example-data-automation.com"
    }
  ];

  const automationCreativeProjects = [
    {
      title: "Automation Explainer Videos",
      description: "Clear, engaging videos showing how automation systems work and their business impact",
      tags: ["Explainer Videos", "Process Visualization", "Business Impact", "Training Content"],
      image: "photo-1581091226825-a6a2a5aee158",
      videoUrl: "dQw4w9WgXcQ"
    },
    {
      title: "Tech Demo Presentations",
      description: "Professional video demonstrations of automation tools and their implementation process",
      tags: ["Tech Demos", "Implementation Guides", "Professional Presentations", "Tool Showcases"],
      image: "photo-1560472354-b33ff0c44a43",
      videoUrl: "dQw4w9WgXcQ"
    }
  ];

  const automationTestimonials = [
    {
      name: "Alex Kim",
      role: "Operations Manager",
      content: "IssyLabs automated our entire workflow. We save hours every day and our error rate dropped to nearly zero!",
      rating: 5,
      image: "photo-1518770660439-4636190af475"
    },
    {
      name: "Priya Singh",
      role: "E-commerce Director",
      content: "The automation suite transformed our order processing and customer service. Our team is now focused on growth, not repetitive tasks.",
      rating: 5,
      image: "photo-1556742049-0cfed4f6a45d"
    }
  ];

  // VideoModal for creative projects
  const VideoModal = ({ videoId, thumbnail, title, description }) => (
    <Dialog>
      <DialogTrigger asChild>
        <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
          <img 
            src={thumbnail} 
            alt={title}
            className="w-full h-64 object-cover"
          />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
            <Play className="h-12 w-12 text-white" />
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl p-0">
        <iframe
          width="100%"
          height="400"
          src={`https://www.youtube.com/embed/${videoId}`}
          title={title}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-2">{title}</h3>
          <p className="text-muted-foreground mb-2">{description}</p>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen premium-gradient-bg">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Settings className="h-6 w-6 text-accent mr-2" />
              IssyLabs Automation
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8 justify-center flex-1">
              {['home','about','services','portfolio','testimonials','contact'].map((item) => (
                <button
                  key={item}
                  onClick={() => document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' })}
                  className={`text-sm font-medium transition-all duration-300 hover:text-accent text-muted-foreground`}
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
            {/* Mobile Menu Button */}
            <Button
              className="md:hidden text-foreground"
              onClick={() => setActiveTab(activeTab === "menu" ? "all" : "menu")}
            >
              {activeTab === "menu" ? <Eye className="h-6 w-6" /> : <Settings className="h-6 w-6" />}
            </Button>
            {/* CTA Button & Back to Home - right aligned with spacing */}
            <div className="flex items-center space-x-4 ml-auto">
              <Button 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
              >
                Start Your Project
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/'} 
                className="hidden md:flex border-accent text-accent hover:bg-accent hover:text-accent-foreground rounded-lg px-6 py-3 font-semibold shadow-md"
              >
                Go to Main Page
              </Button>
            </div>
          </div>
          {/* Mobile Navigation */}
          {activeTab === "menu" && (
            <div className="md:hidden py-4 border-t border-border glass-effect">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => {document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' }); setActiveTab("all");}}
                  className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
                >
                  
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
              
            </div>
          )}
        </div>
      </nav>
     

      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Where Automation
              <br />
              <span className="text-accent float-animation inline-block">
                Drives Growth
              </span>
              <br />
              and Efficiency
              <br />
              <span className="text-primary float-animation inline-block" style={{animationDelay: '1s'}}>
                Powers Success
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
              Automate your business. Optimize your workflow. Unlock new levels of productivity with our unified technical and creative approach for automation solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Automation Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Start My Automation Project
              </Button>
              
            </div>
          </div>
        </div>
      </section>

      {/* About Section - Automation Focused */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              Automation for Modern Business
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
              We build automation systems that save you time, reduce errors, and unlock new growth. Our technical and creative solutions are tailored for every business.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12">
            <Card className="hover-lift glass-effect premium-shadow fade-in-left">
              <div className="p-8">
                <h3 className="text-2xl font-bold text-foreground mb-4">Technical Solutions</h3>
                <p className="text-muted-foreground mb-4">AI-powered platforms, CRM automation, and analytics dashboards.</p>
                <ul className="list-none text-muted-foreground text-left space-y-2">
                  <li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />AI Integration</li>
                  <li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />CRM Automation</li>
                  <li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Data Analytics</li>
                </ul>
              </div>
            </Card>
            <Card className="hover-lift glass-effect premium-shadow fade-in-right">
              <div className="p-8">
                <h3 className="text-2xl font-bold text-foreground mb-4">Creative Services</h3>
                <p className="text-muted-foreground mb-4">Explainer videos, demo presentations, and training content.</p>
                <ul className="list-none text-muted-foreground text-left space-y-2">
                  <li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Explainer Videos</li>
                  <li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Tech Demos</li>
                  <li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Training Content</li>
                </ul>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Technical Solutions + Creative Media
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to automate, optimize, and succeed in your business.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Technical Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-left">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Cpu className="h-10 w-10 text-primary" />
                  <span className="ml-4 text-xl font-bold text-foreground">Automation Tech</span>
                </div>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    AI workflow platforms
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    CRM & lead management
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    E-commerce automation
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Data analytics & reporting
                  </li>
                </ul>
              </CardContent>
            </Card>
            {/* Creative Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-right">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Video className="h-10 w-10 text-accent" />
                  <span className="ml-4 text-xl font-bold text-foreground">Automation Media</span>
                </div>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Explainer & demo videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Training & onboarding content
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Social media automation tips
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Business impact stories
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Automation Projects We've Delivered
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns  see how we deliver complete solutions for automation-driven brands.
            </p>
          </div>
          {/* Technical Automation Projects */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <Cpu className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Automation Tech Solutions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {automationTechnicalProjects.map((project, index) => (
                <Card 
                  key={index} 
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => window.open(project.liveUrl, '_blank')}
                >
                  <CardContent className="p-0">
                    <div 
                      className="h-64 relative overflow-hidden rounded-t-lg"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    </div>
                    <div className="p-6">
                      <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                        {project.title}
                      </h4>
                      <p className="text-muted-foreground mb-4 text-sm">
                        {project.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, i) => (
                          <span key={i} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold">
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Button 
                        size="sm" 
                        className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
                        onClick={() => window.open(project.liveUrl, '_blank')}
                      >
                        View Project
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          {/* Creative Automation Projects */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <Video className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Automation Visual Productions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {automationCreativeProjects.map((project, index) => (
                <Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                  <CardContent className="p-0">
                    <VideoModal
                      videoId={project.videoUrl}
                      thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                      title={project.title}
                      description={project.description}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Automation Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from businesses we've helped automate and optimize.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {automationTestimonials.map((testimonial, index) => (
              <Card key={index} className="glass-effect premium-shadow hover-lift scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section - Deep Blue Background Only */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{backgroundColor: '#0a1433'}}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">Ready to Automate Your Business?</h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{animationDelay: '0.2s'}}>Let's build the perfect tech and creative strategy for your automation needs.</p>
          </div>
          <div className="fade-in-up" style={{animationDelay: '0.4s'}}>
            <ContactForm 
              pageSource="Automation Page"
              businessTypes={["Operations Manager","E-commerce Director","Tech Lead","Business Owner","Startup Founder","Enterprise","Other"]}
            />
          </div>
        </div>
      </section>

      {/* Footer - Match Home Page Style */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Settings className="h-6 w-6 text-accent mr-2" />
                IssyLabs
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Automation Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner  IssyVibe Production.
              </p>
              <p className="text-muted-foreground/80">
                <EMAIL>
              </p>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link to="/" className="text-muted-foreground hover:text-accent transition-colors">Home</Link></li>
                <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li><Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</Link></li>
                <li><Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</Link></li>
                <li><Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</Link></li>
                <li><Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</Link></li>
                <li><Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</Link></li>
                <li><Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</Link></li>
                <li><Link to="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</Link></li>
                <li><Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Automation;
