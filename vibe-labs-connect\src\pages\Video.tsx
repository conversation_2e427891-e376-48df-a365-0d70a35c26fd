import { Video, Camera, Code, CheckCircle, Play, Eye, ChevronRight, Home, Dumbbell } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import ContactForm from "@/components/ContactForm";

const videoTechnicalProjects = [
	{
		title: "Automated Video Editing Platform",
		description: "AI-powered editing, rendering, and delivery for creators and agencies.",
		image: "photo-1464983953574-0892a716854b",
		liveUrl: "#",
		tags: ["Automation", "AI", "Cloud"],
	},
	{
		title: "Media Asset Management",
		description: "Centralized storage, tagging, and sharing for production teams.",
		image: "photo-1519864600265-abb23847ef2c",
		liveUrl: "#",
		tags: ["Dashboard", "Collaboration", "Cloud"],
	},
];
const videoCreativeProjects = [
	{
		title: "Brand Story Films",
		description: "Cinematic video campaigns for brands and agencies.",
		image: "photo-1506744038136-46273834b3fb",
		videoUrl: "dQw4w9WgXcQ",
	},
	{
		title: "Social Media Reels",
		description: "Short-form video content for viral engagement.",
		image: "photo-1518611012118-696072aa579a",
		videoUrl: "dQw4w9WgXcQ",
	},
];
const videoTestimonials = [
	{
		name: "Jordan P.",
		role: "Creative Director",
		content:
			"IssyLabs streamlined our video production and delivered stunning brand films. Our workflow and results are next-level!",
		image: "photo-1464983953574-0892a716854b",
		rating: 5,
	},
	{
		name: "Maya S.",
		role: "Agency Owner",
		content:
			"The automation tools and creative team helped us scale our media business. Highly recommended!",
		image: "photo-1519864600265-abb23847ef2c",
		rating: 5,
	},
	{
		name: "Chris T.",
		role: "Content Creator",
		content:
			"Professional, creative, and reliable. IssyLabs is my go-to for tech and media solutions.",
		image: "photo-1518611012118-696072aa579a",
		rating: 5,
	},
];
const businessTypes = [
	"Video Agency",
	"Content Creator",
	"Brand Manager",
	"Filmmaker",
	"Social Media Manager",
	"Production Studio",
	"Marketing Team",
	"Other",
];

const VideoPage = () => {
	return (
		<div className="min-h-screen premium-gradient-bg">
			{/* Navigation */}
			<nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center py-4">
						<div className="text-2xl font-bold text-foreground flex items-center">
							<Video className="h-6 w-6 text-accent mr-2" />
							IssyLabs Video & Media
						</div>
						<div className="hidden md:flex space-x-8">
							{["home", "about", "services", "portfolio", "testimonials", "contact"].map((item) => (
								<a
									key={item}
									href={`#${item}`}
									className="text-muted-foreground hover:text-accent transition-colors font-medium"
								>
									{item.charAt(0).toUpperCase() + item.slice(1)}
								</a>
							))}
						</div>
						<Button
							onClick={() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })}
							className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
						>
							Start Your Project
						</Button>
					</div>
				</div>
			</nav>
			{/* Back to Home Button */}
			
			{/* Hero Section */}
			<section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
				<div
					className="absolute inset-0 opacity-10"
					style={{
						backgroundImage: `url(https://images.unsplash.com/photo-1464983953574-0892a716854b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
						backgroundSize: "cover",
						backgroundPosition: "center",
					}}
				></div>
				<div className="max-w-7xl mx-auto text-center relative z-10">
					<div className="fade-in-up">
						<h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
							Where Video
							<br />
							<span className="text-accent float-animation inline-block">Inspires Brands</span>
							<br />
							and Technology
							<br />
							<span className="text-primary float-animation inline-block" style={{ animationDelay: "1s" }}>
								Powers Creativity
							</span>
						</h1>
						<p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
							Launch your media brand. Scale your content. Build something iconic with our unified technical and creative approach for video professionals.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								size="lg"
								onClick={() => document.getElementById("portfolio")?.scrollIntoView({ behavior: "smooth" })}
								className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
							>
								<Eye className="mr-2 h-5 w-5" />
								See Video Work
								<ChevronRight className="ml-2 h-5 w-5" />
							</Button>
							<Button
								size="lg"
								variant="outline"
								onClick={() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })}
								className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
							>
								<Home className="mr-2 h-5 w-5" />
								Start My Video Project
							</Button>
						</div>
					</div>
				</div>
			</section>
			{/* About Section */}
			<section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
				<div className="max-w-7xl mx-auto text-center">
					<div className="fade-in-up">
						<h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
							One Team. Two Forces. Unified Media Execution.
						</h2>
						<p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
							IssyLabs combines technical excellence with creative storytelling to deliver complete digital solutions for video and media brands.
						</p>
					</div>
					<div className="grid lg:grid-cols-2 gap-12">
						{/* IssyLabs for Video */}
						<Card className="hover-lift glass-effect premium-shadow scale-in">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Code className="h-10 w-10 text-primary" />
								</div>
								<p className="text-muted-foreground mb-6">
									Your digital backbone for speed, structure, and growth in media.
								</p>
								<ul className="space-y-3 text-muted-foreground">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Automated video editing & delivery
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Media asset management
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Collaboration dashboards
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Cloud storage & sharing
									</li>
								</ul>
							</CardContent>
						</Card>
						{/* IssyVibe for Video */}
						<Card className="hover-lift glass-effect premium-shadow scale-in" style={{ animationDelay: "0.2s" }}>
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Camera className="h-10 w-10 text-accent" />
								</div>
								<p className="text-muted-foreground mb-6">
									Creative services powered by our in-house partner — bringing your brand to life through compelling video and content.
								</p>
								<ul className="space-y-3 text-muted-foreground">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Brand story films
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Social media reels
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Promotional & marketing videos
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Influencer collaborations
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Event coverage & interviews
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>
			{/* Services Section */}
			<section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
							Technical Solutions + Creative Media
						</h2>
						<p className="text-xl text-muted-foreground">
							Everything you need to launch, scale, and succeed in media.
						</p>
					</div>
					<div className="grid lg:grid-cols-2 gap-12 mb-20">
						{/* Technical Services */}
						<Card className="hover-lift glass-effect premium-shadow fade-in-left">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Code className="h-10 w-10 text-primary" />
									<span className="ml-4 text-xl font-bold text-foreground">Video Tech</span>
								</div>
								<ul className="space-y-3 text-muted-foreground">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Automated editing & delivery
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Asset management & cloud storage
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Collaboration dashboards
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Workflow automation
									</li>
								</ul>
							</CardContent>
						</Card>
						{/* Creative Services */}
						<Card className="hover-lift glass-effect premium-shadow fade-in-right">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Camera className="h-10 w-10 text-accent" />
									<span className="ml-4 text-xl font-bold text-foreground">Media Production</span>
								</div>
								<ul className="space-y-3 text-muted-foreground">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Brand story films
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Social media reels
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Promotional & marketing videos
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Event coverage & interviews
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Influencer collaborations
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>
			{/* Portfolio Section */}
			<section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl font-bold text-foreground mb-6">
							Video Projects We've Delivered
						</h2>
						<p className="text-xl text-muted-foreground">
							Technical excellence meets creative campaigns — see how we deliver complete solutions for media brands.
						</p>
					</div>
					{/* Technical Video Projects */}
					<div className="mb-20">
						<div className="flex items-center mb-12">
							<div className="p-3 rounded-full bg-primary/20 mr-4">
								<Code className="h-8 w-8 text-primary" />
							</div>
							<h3 className="text-3xl font-bold text-foreground">Video Tech Solutions</h3>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							{videoTechnicalProjects.map((project, index) => (
								<Card
									key={index}
									className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
									style={{ animationDelay: `${index * 0.1}s` }}
									onClick={() => window.open(project.liveUrl, "_blank")}
								>
									<CardContent className="p-0">
										<div
											className="h-64 relative overflow-hidden rounded-t-lg"
											style={{
												backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
												backgroundSize: "cover",
												backgroundPosition: "center",
											}}
										>
											<div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
										</div>
										<div className="p-6">
											<h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
												{project.title}
											</h4>
											<p className="text-muted-foreground mb-4 text-sm">
												{project.description}
											</p>
											<div className="flex flex-wrap gap-2 mb-4">
												{project.tags.map((tag, i) => (
													<span
														key={i}
														className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold"
													>
														{tag}
													</span>
												))}
											</div>
											<Button
												size="sm"
												className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
												onClick={() => window.open(project.liveUrl, "_blank")}
											>
												View Project
												<ChevronRight className="ml-2 h-4 w-4" />
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
					{/* Creative Video Projects */}
					<div>
						<div className="flex items-center mb-12">
							<div className="p-3 rounded-full bg-accent/20 mr-4">
								<Video className="h-8 w-8 text-accent" />
							</div>
							<h3 className="text-3xl font-bold text-foreground">Media Visual Productions</h3>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							{videoCreativeProjects.map((project, index) => (
								<Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{ animationDelay: `${index * 0.1}s` }}>
									<CardContent className="p-0">
										<Dialog>
											<DialogTrigger asChild>
												<div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
													<img src={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`} alt={project.title} className="w-full h-64 object-cover" />
													<div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
														<div className="bg-primary/90 rounded-full p-4 glow-primary">
															<Play className="w-8 h-8 text-white fill-white" />
														</div>
													</div>
													<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
														<h3 className="text-white font-semibold text-lg">{project.title}</h3>
														<p className="text-white text-sm">{project.description}</p>
													</div>
												</div>
											</DialogTrigger>
											<DialogContent className="max-w-4xl w-full bg-card border-border">
												<div className="aspect-video w-full">
													<iframe src={`https://www.youtube.com/embed/${project.videoUrl}?autoplay=1`} title={project.title} allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen className="w-full h-full rounded-lg" />
												</div>
											</DialogContent>
										</Dialog>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</div>
			</section>
			{/* Testimonials Section */}
			<section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl font-bold text-foreground mb-6">
							Media Success Stories
						</h2>
						<p className="text-xl text-muted-foreground">
							Real results from media professionals we've helped transform their business.
						</p>
					</div>
					<div className="grid md:grid-cols-3 gap-8">
						{videoTestimonials.map((testimonial, index) => (
							<Card key={index} className="glass-effect premium-shadow hover-lift scale-in" style={{ animationDelay: `${index * 0.1}s` }}>
								<CardContent className="p-8">
									<div className="flex items-center mb-4">
										<div
											className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
											style={{
												backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`,
											}}
										></div>
										<div>
											<div className="font-bold text-foreground">{testimonial.name}</div>
											<div className="text-muted-foreground text-sm">{testimonial.role}</div>
										</div>
									</div>
									<p className="text-muted-foreground italic mb-6">"{testimonial.content}"</p>
									<div className="flex gap-1">
										{[...Array(testimonial.rating)].map((_, i) => (
											<CheckCircle key={i} className="h-4 w-4 text-accent" />
										))}
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>
			{/* Contact Section */}
			<section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: "#0a1433" }}>
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16">
						<h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">
							Ready to Transform Your Media Business?
						</h2>
						<p className="text-xl text-muted-foreground fade-in-up" style={{ animationDelay: "0.2s" }}>
							Let's build the perfect tech and creative strategy for your brand or studio.
						</p>
					</div>
					<div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
						<ContactForm pageSource="Video & Media Page" businessTypes={businessTypes} />
					</div>
				</div>
			</section>
			{/* Footer */}
			<footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
				<div className="max-w-7xl mx-auto">
					<div className="grid md:grid-cols-4 gap-8">
						<div className="md:col-span-2">
							<div className="text-2xl font-bold text-foreground mb-4 flex items-center">
								<Video className="h-6 w-6 text-accent mr-2" />
								IssyLabs
							</div>
							<p className="text-muted-foreground mb-4">
								Your All-in-One Video & Media Tech & Creative Brand System
							</p>
							<p className="text-muted-foreground/80 mb-4">
								Creative services are powered by our in-house partner — IssyVibe Production.
							</p>
							<p className="text-muted-foreground/80"><EMAIL></p>
						</div>
						<div>
							<h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
							<ul className="space-y-2">
								<li><a href="#home" className="hover:text-accent">Home</a></li>
								<li><a href="#about" className="hover:text-accent">About</a></li>
								<li><a href="#services" className="hover:text-accent">Services</a></li>
								<li><a href="#portfolio" className="hover:text-accent">Portfolio</a></li>
								<li><a href="#testimonials" className="hover:text-accent">Testimonials</a></li>
								<li><a href="#contact" className="hover:text-accent">Contact</a></li>
							</ul>
						</div>
						<div>
							<h4 className="text-foreground font-semibold mb-4">Industries</h4>
							<ul className="space-y-2">
								<li>Real Estate</li>
								<li>Fashion</li>
								<li>Music</li>
								<li>Video & Media</li>
								<li>Automation</li>
								<li>Coaching</li>
								<li>Partner</li>
								<li>Gym & Fitness</li>
							</ul>
						</div>
					</div>
					<div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
						<p>&copy; 2025 IssyLabs. All rights reserved.</p>
					</div>
				</div>
			</footer>
		</div>
	);
};

export default VideoPage;
