

import { useState } from "react";
import { Monitor, Eye, ChevronRight, Play, Star } from "lucide-react";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import ContactForm from "@/components/ContactForm";

const techTechnicalProjects = [
  {
    title: "AI-Powered Analytics Platform",
    description: "Advanced analytics and machine learning for tech businesses.",
    image: "photo-1519389950473-47ba0277781c",
    liveUrl: "https://example-ai-analytics.com",
    tags: ["AI", "Analytics", "Platform"],
  },
  {
    title: "Cloud DevOps Dashboard",
    description: "Manage cloud infrastructure and deployments with ease.",
    image: "photo-1461749280684-dccba630e2f6",
    liveUrl: "https://example-devops-dashboard.com",
    tags: ["Cloud", "DevOps", "Dashboard"],
  },
];

const techCreativeProjects = [
  {
    title: "Tech Brand Video",
    description: "Creative video production for technology brands.",
    image: "photo-1504384308090-c894fdcc538d",
    videoId: "dQw4w9WgXcQ",
    tags: ["Video", "Branding", "Tech"],
  },
];

const techTestimonials = [
  {
    name: "Alex T.",
    role: "CTO, Startup",
    content:
      "IssyLabs delivered our AI analytics platform and a stunning brand video. Our tech team loves the results!",
    image: "photo-1519389950473-47ba0277781c",
    rating: 5,
  },
];

const businessTypes = [
  "Tech Startup",
  "Software Company",
  "IT Consultant",
  "Other",
];

const projects = [
  ...techTechnicalProjects.map((p) => ({ ...p, category: "technical" })),
  ...techCreativeProjects.map((p) => ({ ...p, category: "creative" })),
];

const VideoModal = ({ videoId, thumbnail, title, description }) => (
  <Dialog>
    <DialogTrigger asChild>
      <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
        <img 
          src={`https://images.unsplash.com/${thumbnail}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`} 
          alt={title}
          className="w-full h-64 object-cover"
        />
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
          <div className="bg-primary/90 rounded-full p-4 glow-primary">
            <Play className="w-8 h-8 text-white fill-white" />
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <h3 className="text-white font-semibold text-lg">{title}</h3>
          {description && (
            <p className="text-white/80 text-xs mt-1">{description}</p>
          )}
        </div>
      </div>
    </DialogTrigger>
    <DialogContent className="max-w-4xl w-full bg-card border-border">
      <div className="aspect-video w-full">
        <iframe
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="w-full h-full rounded-lg"
        />
      </div>
    </DialogContent>
  </Dialog>
);

const Tech = () => {
  const [activeTab, setActiveTab] = useState("all");
  const filteredProjects = activeTab === "all" ? projects : projects.filter(p => p.category === activeTab);

  return (
    <div className="min-h-screen premium-gradient-bg">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Monitor className="h-6 w-6 text-accent mr-2" />
              IssyLabs Finance
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' })}
                  className={`text-sm font-medium transition-all duration-300 hover:text-accent text-muted-foreground`}
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
            {/* Mobile Menu Button */}
            <Button
              className="md:hidden text-foreground"
              onClick={() => setActiveTab(activeTab === "menu" ? "all" : "menu")}
            >
              {activeTab === "menu" ? <Eye className="h-6 w-6" /> : <Monitor className="h-6 w-6" />}
            </Button>
            {/* CTA Button */}
            <Button 
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
            >
              Start Your Project
            </Button>
          </div>
          {/* Mobile Navigation */}
          {activeTab === "menu" && (
            <div className="md:hidden py-4 border-t border-border glass-effect">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => { document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' }); setActiveTab("all"); }}
                  className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1515165562835-cf7747c1e9fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Where Finance
              <br />
              <span className="text-accent float-animation inline-block">
                Trades
              </span>
              <br />
              and Wealth
              <br />
              <span className="text-primary float-animation inline-block" style={{animationDelay: '1s'}}>
                Grows
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
              Launch your finance business. Scale your platform. Build something iconic with our unified technical and creative approach for finance professionals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Finance Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section - Finance Focused */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              One Team. Two Forces. Unified Finance Execution.
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
              IssyLabs combines technical excellence with creative storytelling to deliver complete digital solutions for finance brands.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12">
            {/* IssyLabs for Finance */}
            <div className="hover-lift glass-effect premium-shadow scale-in p-8 bg-card">
              <div className="flex items-center mb-6">
                <Monitor className="h-10 w-10 text-primary mr-4" />
                <div>
                  <h3 className="text-2xl font-bold text-foreground">Finance Tech Solutions</h3>
                  <p className="text-muted-foreground">IssyLabs for Finance</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-6">
                Your digital backbone for speed, structure, and growth in the finance industry.
              </p>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Trading dashboards & analytics
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Investor portals & secure logins
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Custom finance web and mobile applications
                </li>
              </ul>
            </div>
            {/* IssyVibe for Finance */}
            <div className="hover-lift glass-effect premium-shadow scale-in p-8 bg-card" style={{animationDelay: '0.2s'}}>
              <div className="flex items-center mb-6">
                <Monitor className="h-10 w-10 text-accent mr-4" />
                <div>
                  <h3 className="text-2xl font-bold text-foreground">Finance Visual Production</h3>
                  <p className="text-muted-foreground">IssyVibe for Finance</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-6">
                Creative services powered by our in-house partner bringing your finance brand to life through compelling visuals and content.
              </p>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Brand videos & campaign showcase
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Financial product photography
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Social media content & reels
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Technical Solutions + Creative Media
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to launch, scale, and succeed in the finance industry.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Technical Services */}
            <div className="hover-lift glass-effect premium-shadow fade-in-left p-8 bg-card">
              <div className="flex items-center mb-6">
                <Monitor className="h-10 w-10 text-primary" />
                <span className="ml-4 text-xl font-bold text-foreground">Finance Tech</span>
              </div>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Trading dashboards & analytics
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Investor portals & secure logins
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-primary mr-2">•</span>
                  Custom finance web and mobile applications
                </li>
              </ul>
            </div>
            {/* Creative Services */}
            <div className="hover-lift glass-effect premium-shadow fade-in-right p-8 bg-card">
              <div className="flex items-center mb-6">
                <Monitor className="h-10 w-10 text-accent" />
                <span className="ml-4 text-xl font-bold text-foreground">Finance Media</span>
              </div>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Brand videos & campaign showcase
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Financial product photography
                </li>
                <li className="flex items-center">
                  <span className="h-4 w-4 text-accent mr-2">•</span>
                  Social media content & reels
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Finance Projects We've Delivered
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns—see how we deliver complete solutions for finance brands.
            </p>
          </div>
          {/* Technical Finance Projects */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <Monitor className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Finance Tech Solutions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {techTechnicalProjects.map((project, index) => (
                <div 
                  key={index} 
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in bg-card"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => window.open(project.liveUrl, '_blank')}
                >
                  <div className="h-64 relative overflow-hidden rounded-t-lg"
                    style={{
                      backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                      {project.title}
                    </h4>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {project.description}
                    </p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.tags.map((tag, i) => (
                        <span key={i} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold">
                          {tag}
                        </span>
                      ))}
                    </div>
                    <Button 
                      size="sm" 
                      className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
                      onClick={() => window.open(project.liveUrl, '_blank')}
                    >
                      View Project
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Creative Finance Projects */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <Monitor className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Finance Visual Productions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {techCreativeProjects.map((project, index) => (
                <div key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in bg-card" style={{animationDelay: `${index * 0.1}s`}}>
                  <VideoModal
                    videoId={project.videoId}
                    thumbnail={project.image}
                    title={project.title}
                    description={project.description}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Finance Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from finance professionals we've helped transform their business.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {techTestimonials.map((testimonial, index) => (
              <div key={index} className="glass-effect premium-shadow hover-lift scale-in bg-card" style={{animationDelay: `${index * 0.1}s`}}>
                <div className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section - Deep Blue Background Only */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{backgroundColor: '#0a1433'}}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">Ready to Transform Your Finance Brand?</h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{animationDelay: '0.2s'}}>Let's build the perfect tech and creative strategy for your finance business.</p>
          </div>
          <div className="fade-in-up" style={{animationDelay: '0.4s'}}>
            <ContactForm 
              pageSource="Finance Page"
              businessTypes={businessTypes}
            />
          </div>
        </div>
      </section>

      {/* Footer - Match Home Page Style */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Monitor className="h-6 w-6 text-accent mr-2" />
                IssyLabs
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Finance Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner IssyVibe Production.
              </p>
              <br />
             <p className="text-muted-foreground/80 size-full text-4xl font-bold ">
                <EMAIL>
              </p>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="/" className="text-muted-foreground hover:text-accent transition-colors">Home</a></li>
                <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li><a href="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</a></li>
                <li><a href="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</a></li>
                <li><a href="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</a></li>
                <li><a href="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</a></li>
                <li><a href="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</a></li>
                <li><a href="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</a></li>
                <li><a href="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</a></li>
                <li><a href="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Tech;
