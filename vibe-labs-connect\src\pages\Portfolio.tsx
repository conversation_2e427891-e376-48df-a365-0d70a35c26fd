
import { ArrowLeft, Code, Video, ExternalLink, Filter, <PERSON>rk<PERSON>, Eye } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useState } from "react";

const Portfolio = () => {
  const [activeFilter, setActiveFilter] = useState("all");

  const projects = [
    {
      id: 1,
      title: "Fashion E-commerce Platform + Brand Campaign",
      category: "fashion",
      description: "Complete online boutique with professional product photography and social media content",
      techServices: ["Custom E-commerce", "Inventory Management", "CRM Integration"],
      mediaServices: ["Product Photography", "Brand Videos", "Instagram Reels"],
      image: "photo-1441986300917-64674bd600d8"
    },
    {
      id: 2,
      title: "Real Estate CRM + Virtual Tour Production",
      category: "realestate",
      description: "Property management system with immersive virtual tours and agent promotional videos",
      techServices: ["CRM Dashboard", "Analytics", "Automation"],
      mediaServices: ["Virtual Tours", "Agent Videos", "Property Marketing"],
      image: "photo-1560518883-ce09059eeffa"
    },
    {
      id: 3,
      title: "Music Artist Platform + Music Videos",
      category: "music",
      description: "Streaming platform with music video production and social media content creation",
      techServices: ["Artist Platform", "Streaming System", "Fan Management"],
      mediaServices: ["Music Videos", "Visual Content", "Social Media"],
      image: "photo-1493225457124-a3eb161ffa5f"
    },
    {
      id: 4,
      title: "Coaching LMS + Course Video Production",
      category: "coaching",
      description: "Learning management system with professional course videos and marketing content",
      techServices: ["LMS Platform", "Booking System", "Payment Processing"],
      mediaServices: ["Course Videos", "Promotional Content", "Testimonial Videos"],
      image: "photo-1507003211169-0a1dd7228f2d"
    },
    {
      id: 5,
      title: "Video Creator Dashboard + Content Production",
      category: "video",
      description: "Content management platform with video production and editing services",
      techServices: ["Content CMS", "Analytics Dashboard", "Monetization Tools"],
      mediaServices: ["Video Production", "Editing Services", "Thumbnail Design"],
      image: "photo-1574717024653-61fd2cf4d44d"
    },
    {
      id: 6,
      title: "Restaurant Automation + Food Photography",
      category: "automation",
      description: "Complete restaurant management system with professional food photography and marketing",
      techServices: ["Order Management", "Kitchen Automation", "Analytics"],
      mediaServices: ["Food Photography", "Marketing Videos", "Social Content"],
      image: "photo-1518186285589-2f7649de83e0"
    }
  ];

  const filters = [
    { id: "all", label: "All Projects" },
    { id: "fashion", label: "Fashion" },
    { id: "realestate", label: "Real Estate" },
    { id: "music", label: "Music" },
    { id: "coaching", label: "Coaching" },
    { id: "video", label: "Video Creation" },
    { id: "automation", label: "Automation" }
  ];

  const filteredProjects = activeFilter === "all" 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <div className="min-h-screen premium-gradient-bg">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link to="/" className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors">
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Home</span>
            </Link>
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Sparkles className="h-6 w-6 text-accent mr-2" />
              Portfolio
            </div>
            <Button asChild className="bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift">
              <Link to="/contact">Start Your Project</Link>
            </Button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
        
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Our Work Speaks
              <br />
              <span className="text-accent float-animation">
                For Itself
              </span>
            </h1>
            <p className="text-xl text-white/90 mb-12 max-w-3xl mx-auto">
              Technical excellence meets creative brilliance — see how we deliver complete solutions across every industry.
            </p>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="px-4 sm:px-6 lg:px-8 mb-12">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-4">
            {filters.map((filter) => (
              <Button
                key={filter.id}
                onClick={() => setActiveFilter(filter.id)}
                variant={activeFilter === filter.id ? "default" : "outline"}
                className={`${
                  activeFilter === filter.id
                    ? "bg-accent text-accent-foreground hover:bg-accent/90 shadow-lg"
                    : "border-border bg-background/50 text-muted-foreground hover:bg-background/80 backdrop-blur-sm"
                }`}
              >
                <Filter className="h-4 w-4 mr-2" />
                {filter.label}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Combined Projects Grid */}
      <section className="px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {filteredProjects.map((project) => (
              <Card 
                key={project.id}
                className="glass-effect premium-shadow hover-lift group cursor-pointer scale-in"
                style={{animationDelay: `${project.id * 0.1}s`}}
              >
                <CardContent className="p-0">
                  <div 
                    className="h-64 relative overflow-hidden rounded-t-lg"
                    style={{
                      backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    <div className="absolute bottom-4 right-4 flex space-x-2">
                      <div className="p-2 bg-primary/20 backdrop-blur-sm rounded-full">
                        <Code className="h-5 w-5 text-white" />
                      </div>
                      <div className="p-2 bg-accent/20 backdrop-blur-sm rounded-full">
                        <Video className="h-5 w-5 text-white" />
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {project.description}
                    </p>
                    
                    <div className="mb-3">
                      <p className="text-xs font-medium text-primary mb-2 flex items-center">
                        <Code className="h-3 w-3 mr-1" />
                        Technical Solutions
                      </p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {project.techServices.map((service, index) => (
                          <span 
                            key={index}
                            className="bg-primary/10 text-primary px-2 py-1 rounded text-xs"
                          >
                            {service}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <p className="text-xs font-medium text-accent mb-2 flex items-center">
                        <Video className="h-3 w-3 mr-1" />
                        Creative Media
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {project.mediaServices.map((service, index) => (
                          <span 
                            key={index}
                            className="bg-accent/10 text-accent px-2 py-1 rounded text-xs"
                          >
                            {service}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center text-primary font-medium text-sm group-hover:text-accent transition-colors">
                      View Complete Project
                      <ExternalLink className="ml-1 h-4 w-4" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-4xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-8">
              Ready to Join Our Portfolio?
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Let's create something remarkable together — technical excellence meets creative brilliance.
            </p>
            <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground font-semibold px-12 py-6 text-xl shadow-lg hover-lift pulse-glow">
              <Link to="/contact">
                <Eye className="mr-2 h-6 w-6" />
                Start Your Project
                <ExternalLink className="ml-2 h-6 w-6" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portfolio;
