import Travel from "./pages/Travel";
import Tech from "./pages/Tech";
import Finance from "./pages/Finance";
import Restaurant from "./pages/Restaurant";

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Fashion from "./pages/Fashion";
import RealEstate from "./pages/RealEstate";
import Music from "./pages/Music";
import Coaching from "./pages/Coaching";
import Video from "./pages/Video";
import Automation from "./pages/Automation";
import Gym from "./pages/Gym";
import Portfolio from "./pages/Portfolio";
import Contact from "./pages/Contact";
import Partner from "./pages/Partner";
import NotFound from "./pages/NotFound";
import Healthcare from "./pages/Healthcare";
import Legal from "./pages/Legal";
import Innovation from "./pages/Innovation";
import Beauty from "./pages/Beauty";
import Farming from "./pages/Farming";
import Hospitality from "./pages/Hospitality";
import NGO from "./pages/NGO";
import Education from "./pages/Education";
import Logistics from "./pages/Logistics";
import SaaS from "./pages/SaaS";
import Events from "./pages/Events";




const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/fashion" element={<Fashion />} />
          <Route path="/real-estate" element={<RealEstate />} />
          <Route path="/music" element={<Music />} />
          <Route path="/coaching" element={<Coaching />} />
          <Route path="/video" element={<Video />} />
          <Route path="/travel" element={<Travel />} />
          <Route path="/tech" element={<Tech />} />
          <Route path="/finance" element={<Finance />} />
          <Route path="/restaurant" element={<Restaurant />} />
          <Route path="/automation" element={<Automation />} />
          <Route path="/gym" element={<Gym />} />
          <Route path="/portfolio" element={<Portfolio />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/partner" element={<Partner />} />
          <Route path="/healthcare" element={<Healthcare />} />
          <Route path="/legal" element={<Legal />} />
          <Route path="/innovation" element={<Innovation />} />
          <Route path="/beauty" element={<Beauty />} />
          <Route path="/farming" element={<Farming />} />
          <Route path="/hospitality" element={<Hospitality />} />
          <Route path="/ngo" element={<NGO />} />
          <Route path="/education" element={<Education />} />
          <Route path="/logistics" element={<Logistics />} />
          <Route path="/saas" element={<SaaS />} />
          <Route path="/events" element={<Events />} />
          <Route path="/travel" element={<Travel />} />
          <Route path="/tech" element={<Tech />} />
          <Route path="/finance" element={<Finance />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
