import { ArrowLeft, ChevronRight, Video, Camera, Music, Edit, Mic, Film, Star, CheckCircle, Mail, Phone, Play, Eye, ExternalLink } from "lucide-react";
import ContactForm from "@/components/ContactForm";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import { useState } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const Partner = () => {
  const [activeTab, setActiveTab] = useState("all");

  const services = [
    {
      icon: Video,
      title: "Short-form Content",
      description: "Reels, TikToks, and viral video content that captures attention and drives engagement.",
      features: ["Instagram Reels", "TikTok Videos", "YouTube Shorts", "Story Content"]
    },
    {
      icon: Camera,
      title: "Product & Lifestyle Shoots",
      description: "Professional photography and videography that showcases your brand in the best light.",
      features: ["Product Photography", "Lifestyle Content", "Brand Imagery", "Commercial Shoots"]
    },
    {
      icon: Music,
      title: "Music & Audio Production",
      description: "From soundtracks to voiceovers, we create audio that enhances your visual story.",
      features: ["Music Videos", "Voiceovers", "Sound Design", "Audio Editing"]
    },
    {
      icon: Edit,
      title: "Post-Production",
      description: "Professional editing, color grading, and motion graphics for polished final products.",
      features: ["Video Editing", "Color Grading", "Motion Graphics", "Visual Effects"]
    },
    {
      icon: Mic,
      title: "Interviews & Testimonials",
      description: "Compelling talking-head content that builds trust and showcases your expertise.",
      features: ["Client Testimonials", "Expert Interviews", "Podcast Content", "Case Studies"]
    },
    {
      icon: Film,
      title: "Campaign Production",
      description: "Full-scale video campaigns from concept to delivery, tailored to your brand goals.",
      features: ["Brand Campaigns", "Launch Videos", "Promo Content", "Commercial Production"]
    }
  ];

  const partnerTechnicalProjects = [
    {
      title: "Brand Content Platform",
      description: "Custom dashboard for managing campaigns, assets, and analytics.",
      tags: ["Content Management", "Analytics", "Campaign Dashboard", "Brand Assets"],
      image: "photo-1574717024653-61fd2cf4d44d",
      liveUrl: "https://example-partner-content.com"
    },
    {
      title: "Automated Video Distribution",
      description: "Automated publishing and analytics for short-form video content.",
      tags: ["Video Automation", "Publishing", "Analytics", "Multi-Platform"],
      image: "photo-1507003211169-0a1dd7228f2d",
      liveUrl: "https://example-partner-video.com"
    }
  ];
  const partnerCreativeProjects = [
    {
      title: "Viral Campaign Videos",
      description: "Short-form content for TikTok, Instagram, and YouTube Shorts.",
      tags: ["Viral Videos", "Short-form", "Social Media", "Brand Campaign"],
      image: "photo-1581091226825-a6a2a5aee158",
      videoUrl: "dQw4w9WgXcQ"
    },
    {
      title: "Product & Lifestyle Shoots",
      description: "Professional photography and video for brand launches.",
      tags: ["Product Photography", "Lifestyle Content", "Brand Imagery", "Commercial Shoots"],
      image: "photo-1516321318423-f06f85e504b3",
      videoUrl: "dQw4w9WgXcQ"
    }
  ];
  const partnerTestimonials = [
    {
      name: "Sophie Tran",
      role: "Brand Manager",
      content: "IssyVibe's campaign videos went viral and drove massive engagement. The dashboard made tracking results easy!",
      rating: 5,
      image: "photo-1574717024653-61fd2cf4d44d"
    },
    {
      name: "David Lee",
      role: "Content Creator",
      content: "The automated publishing system saved me hours every week. IssyLabs and IssyVibe are the dream team for creators!",
      rating: 5,
      image: "photo-1507003211169-0a1dd7228f2d"
    }
  ];
  const VideoModal = ({ videoId, thumbnail, title, description }) => (
    <Dialog>
      <DialogTrigger asChild>
        <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
          <img 
            src={thumbnail} 
            alt={title}
            className="w-full h-64 object-cover"
          />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
            <Play className="h-12 w-12 text-white" />
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-2xl p-0">
        <iframe
          width="100%"
          height="400"
          src={`https://www.youtube.com/embed/${videoId}`}
          title={title}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        ></iframe>
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-2">{title}</h3>
          <p className="text-muted-foreground mb-2">{description}</p>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen premium-gradient-bg">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Film className="h-6 w-6 text-accent mr-2" />
              IssyVibe Partner
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' })}
                  className={`text-sm font-medium transition-all duration-300 hover:text-accent text-muted-foreground`}
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
            {/* Mobile Menu Button */}
            <Button
              className="md:hidden text-foreground"
              onClick={() => setActiveTab(activeTab === "menu" ? "all" : "menu")}
            >
              {activeTab === "menu" ? <Eye className="h-6 w-6" /> : <Film className="h-6 w-6" />}
            </Button>
            {/* CTA Button */}
            <Button 
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold"
            >
              Book IssyVibe
            </Button>
          </div>
          {/* Mobile Navigation */}
          {activeTab === "menu" && (
            <div className="md:hidden py-4 border-t border-border glass-effect">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => {document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' }); setActiveTab("all");}}
                  className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
          )}
        </div>
      </nav>
      {/* Back to Home Button - Centered Below Navbar */}
      
      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Partner With IssyVibe
              <br />
              <span className="text-accent float-animation inline-block">
                For Viral Content
              </span>
              <br />
              and Brand Growth
              <br />
              <span className="text-primary float-animation inline-block" style={{animationDelay: '1s'}}>
                Delivered
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
              Create, launch, and scale your brand with our unified technical and creative approach for partners and creators.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Partner Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Camera className="mr-2 h-5 w-5" />
                Book IssyVibe
              </Button>
            </div>
          </div>
        </div>
      </section>
      {/* About Section - Partner Focused */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              Partner Services, Viral Results
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
              Our partner solutions empower brands and creators to launch campaigns, automate content, and drive engagement.
            </p>
          </div>
        </div>
      </section>
      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">Our Partner Services</h2>
            <p className="text-xl text-muted-foreground">From viral campaigns to product shoots, we deliver every creative and technical solution for your brand.</p>
          </div>
          <div className="grid lg:grid-cols-3 gap-12 mb-20">
            {services.map((service, idx) => (
              <Card key={idx} className="hover-lift glass-effect premium-shadow scale-in">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <service.icon className="h-8 w-8 text-accent mr-3" />
                    <h3 className="text-xl font-bold text-foreground">{service.title}</h3>
                  </div>
                  <p className="text-muted-foreground mb-4">{service.description}</p>
                  <ul className="list-none text-muted-foreground text-left space-y-2">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />{feature}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Partner Projects We've Delivered
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns  see how we deliver complete solutions for partners and creators.
            </p>
          </div>
          {/* Technical Partner Projects */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <Camera className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Partner Tech Solutions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {partnerTechnicalProjects.map((project, index) => (
                <Card 
                  key={index} 
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => window.open(project.liveUrl, '_blank')}
                >
                  <CardContent className="p-0">
                    <div 
                      className="h-64 relative overflow-hidden rounded-t-lg"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    </div>
                    <div className="p-6">
                      <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                        {project.title}
                      </h4>
                      <p className="text-muted-foreground mb-4 text-sm">
                        {project.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, i) => (
                          <span key={i} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold">
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Button 
                        size="sm" 
                        className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
                        onClick={() => window.open(project.liveUrl, '_blank')}
                      >
                        View Project
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          {/* Creative Partner Projects */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <Video className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Partner Visual Productions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {partnerCreativeProjects.map((project, index) => (
                <Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                  <CardContent className="p-0">
                    <VideoModal
                      videoId={project.videoUrl}
                      thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                      title={project.title}
                      description={project.description}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Partner Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from brands and creators we've helped go viral and grow.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {partnerTestimonials.map((testimonial, index) => (
              <Card key={index} className="glass-effect premium-shadow hover-lift scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Contact Section - Deep Blue Background Only */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{backgroundColor: '#0a1433'}}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">Ready to Go Viral With IssyVibe?</h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{animationDelay: '0.2s'}}>Let's build the perfect tech and creative strategy for your brand or campaign.</p>
          </div>
          <div className="fade-in-up" style={{animationDelay: '0.4s'}}>
            <ContactForm 
              pageSource="Partner Page"
              businessTypes={["Brand Manager","Content Creator","Agency","Startup","Enterprise","Other"]}
            />
          </div>
        </div>
      </section>
      {/* Footer - Match Home Page Style */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Film className="h-6 w-6 text-accent mr-2" />
                IssyVibe
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Partner Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner  IssyLabs.
              </p>
              <p className="text-muted-foreground/80">
                <EMAIL>
              </p>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link to="/" className="text-muted-foreground hover:text-accent transition-colors">Home</Link></li>
                <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li><Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</Link></li>
                <li><Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</Link></li>
                <li><Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</Link></li>
                <li><Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</Link></li>
                <li><Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</Link></li>
                <li><Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</Link></li>
                <li><Link to="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</Link></li>
                <li><Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Partner;
