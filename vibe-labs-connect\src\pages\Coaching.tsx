import { ArrowLeft, ChevronRight, Code, Video, Users, BookOpen, Calendar, BarChart3, Star, CheckCircle, Mail, Phone, Play, Eye, ExternalLink } from "lucide-react";
import ContactForm from "@/components/ContactForm";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import { useState } from "react";
import { Link } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

const coachingTechnicalProjects = [
	{
		title: "Online Course Platform",
		description:
			"Custom LMS with video hosting, quizzes, and progress tracking for coaches.",
		tags: ["LMS", "Video Hosting", "Quizzes", "Progress Tracking"],
		image: "photo-1516321318423-f06f85e504b3",
		liveUrl: "https://example-coaching-lms.com",
	},
	{
		title: "Automated Booking System",
		description: "Scheduling and payment automation for coaching sessions.",
		tags: [
			"Booking Automation",
			"Payments",
			"Calendar Integration",
			"Client Management",
		],
		image: "photo-1507003211169-0a1dd7228f2d",
		liveUrl: "https://example-coaching-booking.com",
	},
];
const coachingCreativeProjects = [
	{
		title: "Course Promo Videos",
		description: "Engaging promotional videos for online coaching courses.",
		tags: ["Promo Videos", "Course Marketing", "Social Media", "Brand Story"],
		image: "photo-1516321318423-f06f85e504b3",
		videoUrl: "dQw4w9WgXcQ",
	},
	{
		title: "Testimonial Content",
		description: "Client success stories and social proof videos.",
		tags: ["Testimonials", "Social Proof", "Video Editing", "Brand Trust"],
		image: "photo-1507003211169-0a1dd7228f2d",
		videoUrl: "dQw4w9WgXcQ",
	},
];
const coachingTestimonials = [
	{
		name: "Elena Vasquez",
		role: "Business Coach",
		content:
			"The course platform they built is incredible, and the promotional videos they created helped me reach 10x more students. One team, complete solution.",
		rating: 5,
		image: "photo-1438761681033-6461ffad8d80",
	},
	{
		name: "James Lee",
		role: "Fitness Coach",
		content:
			"IssyLabs automated my booking and payments. My clients love the seamless experience and I have more time to coach!",
		rating: 5,
		image: "photo-1507003211169-0a1dd7228f2d",
	},
];
const VideoModal = ({ videoId, thumbnail, title, description }) => (
	<Dialog>
		<DialogTrigger asChild>
			<div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
				<img
					src={thumbnail}
					alt={title}
					className="w-full h-64 object-cover"
				/>
				<div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
					<Play className="h-12 w-12 text-white" />
				</div>
			</div>
		</DialogTrigger>
		<DialogContent className="max-w-2xl p-0">
			<iframe
				width="100%"
				height="400"
				src={`https://www.youtube.com/embed/${videoId}`}
				title={title}
				frameBorder="0"
				allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
				allowFullScreen
			></iframe>
			<div className="p-6">
				<h3 className="text-xl font-semibold mb-2">{title}</h3>
				<p className="text-muted-foreground mb-2">{description}</p>
			</div>
		</DialogContent>
	</Dialog>
);

const Coaching = () => {
	const [activeTab, setActiveTab] = useState("all");

	return (
		<div className="min-h-screen premium-gradient-bg">
			{/* Navigation */}
			<nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center py-4">
						<div className="text-2xl font-bold text-foreground flex items-center">
							<Users className="h-6 w-6 text-accent mr-2" />
							IssyLabs Coaching
						</div>
						{/* Desktop Navigation */}
						<div className="hidden md:flex space-x-8">
							{["home", "about", "services", "portfolio", "testimonials", "contact"].map(
								(item) => (
									<button
										key={item}
										onClick={() =>
											document.getElementById(item)?.scrollIntoView({
												behavior: "smooth",
											})
										}
										className={`text-sm font-medium transition-all duration-300 hover:text-accent text-muted-foreground`}
									>
										{item.charAt(0).toUpperCase() + item.slice(1)}
									</button>
								)
							)}
						</div>
						{/* Mobile Menu Button */}
						<Button
							className="md:hidden text-foreground"
							onClick={() =>
								setActiveTab(activeTab === "menu" ? "all" : "menu")
							}
						>
							{activeTab === "menu" ? (
								<Eye className="h-6 w-6" />
							) : (
								<Users className="h-6 w-6" />
							)}
						</Button>
						{/* CTA Button */}
						<Button
							onClick={() =>
								document.getElementById("contact")?.scrollIntoView({
									behavior: "smooth",
								})
							}
							className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
						>
							Start Your Project
						</Button>
					</div>
					{/* Mobile Navigation */}
					{activeTab === "menu" && (
						<div className="md:hidden py-4 border-t border-border glass-effect">
							{["home", "about", "services", "portfolio", "testimonials", "contact"].map(
								(item) => (
									<button
										key={item}
										onClick={() => {
											document.getElementById(item)?.scrollIntoView({
												behavior: "smooth",
											});
											setActiveTab("all");
										}}
										className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
									>
										{item.charAt(0).toUpperCase() + item.slice(1)}
									</button>
								)
							)}
						</div>
					)}
				</div>
			</nav>
			{/* Back to Home Button - Centered Below Navbar */}
			
			{/* Hero Section */}
			<section
				id="home"
				className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden"
			>
				<div
					className="absolute inset-0 opacity-20"
					style={{
						backgroundImage: `url(https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
						backgroundSize: "cover",
						backgroundPosition: "center",
					}}
				></div>
				<div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
				<div className="max-w-7xl mx-auto text-center relative z-10">
					<div className="fade-in-up">
						<h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
							From Expertise
							<br />
							<span className="text-accent float-animation inline-block">
								to Empire
							</span>
							<br />
							and Impact
							<br />
							<span
								className="text-primary float-animation inline-block"
								style={{ animationDelay: "1s" }}
							>
								Delivered
							</span>
						</h1>
						<p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
							Build your coaching business. Scale your impact. Create a thriving
							ecosystem with our unified technical and creative approach for coaches.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button
								size="lg"
								onClick={() =>
									document.getElementById("portfolio")?.scrollIntoView({
										behavior: "smooth",
									})
								}
								className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
							>
								<Eye className="mr-2 h-5 w-5" />
								See Coaching Work
								<ChevronRight className="ml-2 h-5 w-5" />
							</Button>
							<Button
								size="lg"
								variant="outline"
								onClick={() =>
									document.getElementById("contact")?.scrollIntoView({
										behavior: "smooth",
									})
								}
								className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
							>
								<Calendar className="mr-2 h-5 w-5" />
								Start My Coaching Project
							</Button>
						</div>
					</div>
				</div>
			</section>
			{/* About Section - Coaching Focused */}
			<section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
				<div className="max-w-7xl mx-auto text-center">
					<div className="fade-in-up">
						<h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
							Coaching Focused, Results Driven
						</h2>
						<p className="text-lg md:text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
							Our coaching solutions empower you and your clients, providing the tools and insights needed for transformative growth.
						</p>
					</div>
					<div className="grid lg:grid-cols-2 gap-12">
						<Card className="hover-lift glass-effect premium-shadow fade-in-left">
							<div className="p-8">
								<h3 className="text-2xl font-bold text-foreground mb-4">Technical Solutions</h3>
								<p className="text-muted-foreground mb-4">Custom platforms, booking automation, and client management for coaches and consultants.</p>
								<ul className="list-none text-muted-foreground text-left space-y-2">
									<li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Online Course Platforms</li>
									<li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Automated Booking & Payments</li>
									<li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Client Management Systems</li>
								</ul>
							</div>
						</Card>
						<Card className="hover-lift glass-effect premium-shadow fade-in-right">
							<div className="p-8">
								<h3 className="text-2xl font-bold text-foreground mb-4">Creative Services</h3>
								<p className="text-muted-foreground mb-4">Brand storytelling, promo videos, and testimonial content to grow your coaching business.</p>
								<ul className="list-none text-muted-foreground text-left space-y-2">
									<li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Course Promo Videos</li>
									<li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Testimonial Content</li>
									<li className="flex items-center"><CheckCircle className="h-5 w-5 text-accent mr-2" />Social Media Campaigns</li>
								</ul>
							</div>
						</Card>
					</div>
				</div>
			</section>
			{/* Services Section */}
			<section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
							Technical Solutions + Creative Media
						</h2>
						<p className="text-xl text-muted-foreground">
							Everything you need to launch, scale, and succeed as a coach.
						</p>
					</div>
					<div className="grid lg:grid-cols-2 gap-12 mb-20">
						{/* Technical Services */}
						<Card className="hover-lift glass-effect premium-shadow fade-in-left">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Code className="h-10 w-10 text-primary" />
									<span className="ml-4 text-xl font-bold text-foreground">
										Coaching Tech
									</span>
								</div>
								<ul className="space-y-3 text-muted-foreground">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										LMS & course platforms
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Automated booking & payments
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Progress tracking & analytics
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-primary mr-2" />
										Client management tools
									</li>
								</ul>
							</CardContent>
						</Card>
						{/* Creative Services */}
						<Card className="hover-lift glass-effect premium-shadow fade-in-right">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Video className="h-10 w-10 text-accent" />
									<span className="ml-4 text-xl font-bold text-foreground">
										Coaching Media
									</span>
								</div>
								<ul className="space-y-3 text-muted-foreground">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Promo & testimonial videos
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Social proof & brand content
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Course marketing assets
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-accent mr-2" />
										Client success stories
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>
			{/* Portfolio Section */}
			<section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl font-bold text-foreground mb-6">
							Coaching Projects We've Delivered
						</h2>
						<p className="text-xl text-muted-foreground">
							Technical excellence meets creative campaigns  see how we deliver
							complete solutions for coaches.
						</p>
					</div>
					{/* Technical Coaching Projects */}
					<div className="mb-20">
						<div className="flex items-center mb-12">
							<div className="p-3 rounded-full bg-primary/20 mr-4">
								<Code className="h-8 w-8 text-primary" />
							</div>
							<h3 className="text-3xl font-bold text-foreground">
								Coaching Tech Solutions
							</h3>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							{coachingTechnicalProjects.map((project, index) => (
								<Card
									key={index}
									className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
									style={{ animationDelay: `${index * 0.1}s` }}
									onClick={() => window.open(project.liveUrl, "_blank")}
								>
									<CardContent className="p-0">
										<div
											className="h-64 relative overflow-hidden rounded-t-lg"
											style={{
												backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
												backgroundSize: "cover",
												backgroundPosition: "center",
											}}
										>
											<div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
										</div>
										<div className="p-6">
											<h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
												{project.title}
											</h4>
											<p className="text-muted-foreground mb-4 text-sm">
												{project.description}
											</p>
											<div className="flex flex-wrap gap-2 mb-4">
												{project.tags.map((tag, i) => (
													<span
														key={i}
														className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold"
													>
														{tag}
													</span>
												))}
											</div>
											<Button
												size="sm"
												className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
												onClick={() => window.open(project.liveUrl, "_blank")}
											>
												View Project
												<ChevronRight className="ml-2 h-4 w-4" />
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
					{/* Creative Coaching Projects */}
					<div>
						<div className="flex items-center mb-12">
							<div className="p-3 rounded-full bg-accent/20 mr-4">
								<Video className="h-8 w-8 text-accent" />
							</div>
							<h3 className="text-3xl font-bold text-foreground">
								Coaching Visual Productions
							</h3>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							{coachingCreativeProjects.map((project, index) => (
								<Card
									key={index}
									className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
									style={{ animationDelay: `${index * 0.1}s` }}
								>
									<CardContent className="p-0">
										<VideoModal
											videoId={project.videoUrl}
											thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
											title={project.title}
											description={project.description}
										/>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</div>
			</section>
			{/* Testimonials Section */}
			<section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl font-bold text-foreground mb-6">
							Coaching Success Stories
						</h2>
						<p className="text-xl text-muted-foreground">
							Real results from coaches we've helped transform their business.
						</p>
					</div>
					<div className="grid md:grid-cols-3 gap-8">
						{coachingTestimonials.map((testimonial, index) => (
							<Card
								key={index}
								className="glass-effect premium-shadow hover-lift scale-in"
								style={{ animationDelay: `${index * 0.1}s` }}
							>
								<CardContent className="p-8">
									<div className="flex items-center mb-4">
										<div
											className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
											style={{
												backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`,
											}}
										></div>
										<div className="flex">
											{[...Array(testimonial.rating)].map((_, i) => (
												<Star
													key={i}
													className="h-4 w-4 text-accent fill-current"
												/>
											))}
										</div>
									</div>
									<p className="text-muted-foreground italic mb-6">
										"{testimonial.content}"
									</p>
									<div>
										<p className="font-semibold text-foreground">
											{testimonial.name}
										</p>
										<p className="text-sm text-muted-foreground">
											{testimonial.role}
										</p>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>
			{/* Contact Section - Deep Blue Background Only */}
			<section
				id="contact"
				className="py-20 px-4 sm:px-6 lg:px-8"
				style={{ backgroundColor: "#0a1433" }}
			>
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16">
						<h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">
							Ready to Build Your Coaching Empire?
						</h2>
						<p className="text-xl text-muted-foreground fade-in-up" style={{ animationDelay: "0.2s" }}>
							Let's build the perfect tech and creative strategy for your coaching
							business.
						</p>
					</div>
					<div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
						<ContactForm
							pageSource="Coaching Page"
							businessTypes={[
								"Business Coach",
								"Fitness Coach",
								"Life Coach",
								"Course Creator",
								"Consultant",
								"Other",
							]}
						/>
					</div>
				</div>
			</section>
			{/* Footer - Match Home Page Style */}
			<footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
				<div className="max-w-7xl mx-auto">
					<div className="grid md:grid-cols-4 gap-8">
						<div className="md:col-span-2">
							<div className="text-2xl font-bold text-foreground mb-4 flex items-center">
								<Users className="h-6 w-6 text-accent mr-2" />
								IssyLabs
							</div>
							<p className="text-muted-foreground mb-4">
								Your All-in-One Coaching Tech & Media Brand System
							</p>
							<p className="text-muted-foreground/80 mb-4">
								Creative services are powered by our in-house partner  IssyVibe
								Production.
							</p>
							<p className="text-muted-foreground/80"><EMAIL></p>
						</div>
						<div>
							<h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
							<ul className="space-y-2">
								<li>
									<Link
										to="/"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Home
									</Link>
								</li>
								<li>
									<a
										href="#about"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										About
									</a>
								</li>
								<li>
									<a
										href="#services"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Services
									</a>
								</li>
								<li>
									<a
										href="#portfolio"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Portfolio
									</a>
								</li>
								<li>
									<a
										href="#testimonials"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Testimonials
									</a>
								</li>
								<li>
									<a
										href="#contact"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Contact
									</a>
								</li>
							</ul>
						</div>
						<div>
							<h4 className="text-foreground font-semibold mb-4">Industries</h4>
							<ul className="space-y-2">
								<li>
									<Link
										to="/fashion"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Fashion
									</Link>
								</li>
								<li>
									<Link
										to="/realestate"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Real Estate
									</Link>
								</li>
								<li>
									<Link
										to="/music"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Music
									</Link>
								</li>
								<li>
									<Link
										to="/coaching"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Coaching
									</Link>
								</li>
								<li>
									<Link
										to="/video"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Video Creation
									</Link>
								</li>
								<li>
									<Link
										to="/automation"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Automation
									</Link>
								</li>
								<li>
									<Link
										to="/gym"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Gym & Fitness
									</Link>
								</li>
								<li>
									<Link
										to="/partner"
										className="text-muted-foreground hover:text-accent transition-colors"
									>
										Partner Services
									</Link>
								</li>
							</ul>
						</div>
					</div>
					<div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
						<p>&copy; 2025 IssyLabs. All rights reserved.</p>
					</div>
				</div>
			</footer>
		</div>
	);
};

export default Coaching;
