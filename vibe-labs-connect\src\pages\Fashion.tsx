

import { useState } from "react";
import { Message<PERSON>ircle, <PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON>rendingUp, Users, ShoppingBag, Camera, Star, CheckCircle, Code, Video, Calendar, Mail, Phone, ChevronRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import NavBar from "@/components/NavBar";
import ContactForm from "@/components/ContactForm";
import YouTubeEmbed from "@/components/YouTubeEmbed";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Play } from "lucide-react";
import { FaWhatsapp } from "react-icons/fa";
import { HiOutlineMail } from "react-icons/hi"; // Import the mail icon from react-icons


// Floating WhatsApp Button (same as home)
const FloatingButtonGroup = ({ section }) => {
  const phone = '12494336588';
  const message = encodeURIComponent(`Hello, I am reaching out to you from your fashion page. I would like to know more about your services.`);
  const whatsappUrl = `https://wa.me/${phone}?text=${message}`;
  return (
    <div className="fixed bottom-5 right-32 z-50 flex flex-col items-end gap-4">
      <a
        href={whatsappUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="rounded-full bg-green-500 hover:bg-green-600 shadow-lg w-14 h-14 flex items-center justify-center transition-all"
        aria-label="Chat on WhatsApp"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.967-.94 1.166-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.1 3.2 5.077 4.363.71.306 1.263.489 1.695.626.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/><path fill="currentColor" fillRule="evenodd" d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.34 4.997L2.003 22l5.137-1.343c1.462.8 3.09 1.222 4.864 1.222 5.514 0 9.997-4.483 9.997-9.997 0-2.668-1.04-5.175-2.927-7.062-1.888-1.888-4.395-2.927-7.062-2.927zm-8.001 9.997c0-4.418 3.583-8.001 8.001-8.001 2.137 0 4.146.832 5.656 2.343 1.511 1.51 2.343 3.519 2.343 5.656 0 4.418-3.583 8.001-8.001 8.001-1.548 0-3.04-.44-4.32-1.272l-.307-.19-3.057.799.82-2.995-.2-.314C3.44 15.04 3.003 13.548 3.003 12z" clipRule="evenodd"/></svg>
      </a>
    </div>
  );
};

const Fashion = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [activeSection, setActiveSection] = useState("home");

  const projects = [
    {
      title: "Luxury Boutique E-commerce",
      description: "Complete online store with inventory management, customer profiles, and automated email marketing.",
      category: "technical",
      image: "photo-1441986300917-64674bd600d8",
      tags: ["E-commerce", "CRM", "Email Marketing", "Inventory"],
      liveUrl: "https://minna.framer.website/"
    },
    {
      title: "Fashion Brand Campaign",
      description: "Complete visual campaign with product photography, brand videos, and social media content.",
      category: "creative",
      image: "photo-1445205170230-053b83016050",
      tags: ["Photography", "Brand Video", "Social Media", "Instagram Reels"],
      videoId: "sPhRi5bKLTo?si=ZcX1AO4pJmZQDBHu"
    },
    {
      title: "Fashion Brand Reel",
      description: "Instagram Reel for fashion brand to promote new collection launch and to engage with audience.",
      category: "creative",
      image: "photo-1441984904996-e0b6ba687e04",
      tags: ["Photography", "Brand Video", "Social Media", "Instagram Reels"],
      videoId: "7Mq3ZzSBZwY"
    },
    {
      title: "Designer Portfolio Website",
      description: "Elegant showcase website with booking system for personal styling consultations.",
      category: "technical",
      image: "photo-1483985988355-763728e1935b",
      tags: ["Portfolio", "Booking System", "Responsive Design", "SEO"],
      liveUrl: "https://velaar.framer.website/products/filter/men"
    },
    {
      title: "Fashion store Website",
      description: "Elegant showcase website with booking system for personal styling consultations. This project highlights our ability to create visually stunning and functional e-commerce platforms tailored for fashion brands.",
      category: "technical",
      image: "photo-1558769132-cb1aea458c5e",
      tags: ["Portfolio", "Booking System", "Responsive Design", "SEO"],
      liveUrl: "https://serrena.framer.website/"
    },
    {
      title: "IssyVibe Production (Our Patner)",
      description: "Visual storytelling powerhouse from cinematic brand films to social-ready reels. IssyVibe Production brings fashion, product, and lifestyle visions to life through world-class photography and video content designed to captivate and convert.",
      category: "technical",
      image: "photo-1655926478996-a65b83efe17c?fm",
      tags: ["Portfolio", "Booking System", "Responsive Design", "SEO"],
      liveUrl: "https://issyvibesproduction.vercel.app/"
    },
    {
      title: "Fashion Week Content",
      description: "Behind-the-scenes content, runway footage, and designer interviews for fashion week coverage.",
      category: "creative",
      image: "photo-1509631179647-0177331693ae",
      tags: ["Event Coverage", "Interviews", "Runway", "Social Content", "Fashion Show"],
      videoId: "TWoKt_mW6Eo?si=2T1_Qo_qkN8Lx9oY"
    },
    {
      title: "IssyLabs and IssyVibe Production",
      description: "IssyLabs and IssyVibe Production collaborated on a series of fashion shows, providing comprehensive coverage from behind-the-scenes preparations to runway highlights.",
      category: "creative",
      image: "photo-1641440616490-723c8cc161b8",
      tags: ["Event Coverage", "Interviews", "Runway", "Social Content"],
      videoId: "hUlpDdM-b4A"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Boutique Owner",
      content: "IssyLabs built my entire e-commerce platform while IssyVibe handled all my product photography. The integration was seamless and my sales doubled in 3 months.",
      rating: 5,
      image: "photo-1552699611-e2c208d5d9cf"
    },
    {
      name: "Marcus Style",
      role: "Fashion Designer",
      content: "From my portfolio website to brand videos, they understood my vision perfectly. The creative content they produced elevated my brand significantly.",
      rating: 5,
      image: "photo-1617137984095-74e4e5e3613f"
    },
    {
      name: "Angelina Smith",
      role: "Fashion Store Owner",
      content: "Issylabs and IssyVibe handled all my product photography. The integration was seamless and my sales doubled in 3 months. The creative content they produced elevated my brand significantly.",
      rating: 5,
      image: "photo-1494790108377-be9c29b29330"
    }
  ];

  const businessTypes = [
    "Boutique Owner",
    "Fashion Designer", 
    "Clothing Brand",
    "Fashion Blogger",
    "Personal Stylist",
    "Fashion Photographer",
    "Fashion Retailer",
    "Other"
  ];

  const filteredProjects = activeTab === "all" ? projects : projects.filter(p => p.category === activeTab);

  return (
    <div className="min-h-screen premium-gradient-bg">
      <FloatingButtonGroup section={activeSection} />
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Palette className="h-6 w-6 text-accent mr-2" />
              {/* <img src="/image/cropped_circle_image-removebg-preview.png" alt="IssyLabs Logo" className="h-10 w-auto mr-2" /> */}
              
              IssyLabs Fashion
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' })}
                  className={`text-sm font-medium transition-all duration-300 hover:text-accent text-muted-foreground`}
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
              ))}
            </div>
            {/* Mobile Menu Button */}
            <Button
              className="md:hidden text-foreground"
              onClick={() => setActiveTab(activeTab === "menu" ? "all" : "menu")}
            >
              {activeTab === "menu" ? <Sparkles className="h-6 w-6" /> : <Palette className="h-6 w-6" />}
            </Button>
            {/* CTA Button */}
            <Button 
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
            >
              Start Your Project
            </Button>
          </div>
          {/* Mobile Navigation */}
          {activeTab === "menu" && (
            <div className="md:hidden py-4 border-t border-border glass-effect">
              {["home","about","services","portfolio","testimonials","contact"].map((item) => (
                <button
                  key={item}
                  onClick={() => {document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' }); setActiveTab("all");}}
                  className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </button>
                
              ))}
            </div>
          )}
        </div>
      </nav>
      {/* Back to Home Button - Centered Below Navbar */}
      

      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Where Fashion
              <br />
              <span className="text-accent float-animation inline-block">
                Inspires
              </span>
              <br />
              and Technology
              <br />
              <span className="text-primary float-animation inline-block" style={{animationDelay: '1s'}}>
                Delivers
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
              Launch your brand. Scale your store. Build something iconic with our unified technical and creative approach for fashion businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Sparkles className="mr-2 h-5 w-5" />
                See Fashion Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Start My Fashion Project
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section - Fashion Focused */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              One Team. Two Forces. Unified Fashion Execution.
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
              IssyLabs combines technical excellence with creative storytelling from our partner IssyVibes Production to deliver complete digital solutions for fashion brands.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12">
            {/* IssyLabs for Fashion */}
            <Card className="hover-lift glass-effect premium-shadow scale-in">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-primary/20 mr-4">
                    <Code className="h-10 w-10 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground">Fashion Tech Solutions</h3>
                    <p className="text-muted-foreground">IssyLabs for Fashion</p>
                  </div>
                </div>
                <p className="text-muted-foreground mb-6">
                  Your digital backbone for speed, structure, and growth in fashion.
                </p>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    E-commerce platforms & online stores
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Inventory & order management
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    CRM & customer relationship systems
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Analytics & sales dashboards
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Creation OF Fashion Website & Mobile app
                  </li>
                </ul>
              </CardContent>
            </Card>
            {/* IssyVibe for Fashion */}
            <Card className="hover-lift glass-effect premium-shadow scale-in" style={{animationDelay: '0.2s'}}>
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-accent/20 mr-4">
                    <Video className="h-10 w-10 text-accent" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground">Fashion Visual Production</h3>
                    <p className="text-muted-foreground">IssyVibe for Fashion</p>
                  </div>
                </div>
                <p className="text-muted-foreground mb-6">
                  Creative services powered by our in-house partner bringing your vision to life through compelling visuals and content.
                </p>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Product photography & styling
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Fashion campaign videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Instagram Reels & TikToks
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Runway & event coverage
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Brand storytelling content
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Technical Solutions + Creative Media
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to launch, scale, and succeed in fashion.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Technical Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-left">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Code className="h-10 w-10 text-primary" />
                  <span className="ml-4 text-xl font-bold text-foreground">Fashion Tech</span>
                </div>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    E-commerce platforms
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Inventory & order management
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    CRM & customer systems
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Booking systems for fittings
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Analytics & dashboards
                  </li>
                </ul>
              </CardContent>
            </Card>
            {/* Creative Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-right">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Camera className="h-10 w-10 text-accent" />
                  <span className="ml-4 text-xl font-bold text-foreground">Fashion Media</span>
                </div>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Product photography & styling
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Fashion campaign videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Instagram Reels & TikToks
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Runway & event coverage
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Brand storytelling content
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Fashion Projects We've Delivered
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns see how we deliver complete solutions for fashion brands.
            </p>
          </div>
          {/* Technical Fashion Projects */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <Code className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Fashion Tech Solutions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {projects.filter(p => p.category === "technical").map((project, index) => (
                <Card 
                  key={index} 
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => window.open(project.liveUrl, '_blank')}
                >
                  <CardContent className="p-0">
                    <div 
                      className="h-64 relative overflow-hidden rounded-t-lg"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    </div>
                    <div className="p-6">
                      <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                        {project.title}
                      </h4>
                      <p className="text-muted-foreground mb-4">{project.description}</p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, i) => (
                          <span 
                            key={i} 
                            className="px-2 py-1 rounded text-xs bg-primary/20 text-primary"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => window.open(project.liveUrl, '_blank')}
                        className="border-accent text-accent hover:bg-accent hover:text-accent-foreground hover-lift"
                      >
                        View Live Site
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          {/* Creative Fashion Projects */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <Video className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Fashion Visual Productions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {projects.filter(p => p.category === "creative").map((project, index) => (
                <Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                  <CardContent className="p-0">
                    <VideoModal
                      videoId={project.videoId}
                      thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                      title={project.title}
                      description={project.description}
                    />
                    <div className="p-6">
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, i) => (
                          <span key={i} className="px-2 py-1 rounded text-xs bg-accent/20 text-accent">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Fashion Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from fashion professionals we've helped transform their brand.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="glass-effect premium-shadow hover-lift scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section - Deep Blue Background Only */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{backgroundColor: '#0a1433'}}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">Ready to Transform Your Fashion Brand?</h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{animationDelay: '0.2s'}}>Let's build the perfect tech and creative strategy for your fashion business.</p>
          </div>
          <div className="fade-in-up" style={{animationDelay: '0.4s'}}>
            <ContactForm 
              pageSource="Fashion Page"
              businessTypes={businessTypes}
            />
          </div>
        </div>
      </section>

      {/* Footer - Match Home Page Style */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Palette className="h-6 w-6 text-accent mr-2" />
                IssyLabs Fashion
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Digital, Automation & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner IssyVibe Production, while Tech and automation services are powered by IssyLabs.
              </p>
              <br></br>


               <div className="space-y-4 text-3xl font-bold">
                    {/* Email */}
                    <a
                      href="mailto:<EMAIL>"
                      className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
                    >
                      <HiOutlineMail className="text-gray-500" />
                      <span><EMAIL></span>
                    </a>
              
                    {/* WhatsApp */}
                    <a
                      href="https://wa.me/12494336588"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-green-600 hover:underline"
                    >
                      <FaWhatsapp className="text-green-500" />
                      <span>+1 (249) 433‑6588‬</span>
                    </a>
                  </div>
              
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                {["Home","About","Services","Portfolio","Testimonials","Contact"].map((item) => (
                  <li key={item}>
                    <button
                      className="text-muted-foreground hover:text-accent text-left"
                      onClick={() => document.getElementById(item.toLowerCase())?.scrollIntoView({ behavior: 'smooth' })}
                    >
                      {item}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                {["Fashion","Real Estate","Music","Coaching","Video Creation","Automation","Gym & Fitness","Partner Services"].map((niche) => (
                  <li key={niche}>
                    <span className="text-muted-foreground">{niche}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

// VideoModal for creative projects
const VideoModal = ({ videoId, thumbnail, title, description }) => (
  <Dialog>
    <DialogTrigger asChild>
      <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
        <img 
          src={thumbnail} 
          alt={title}
          className="w-full h-64 object-cover"
        />
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
          <div className="bg-primary/90 rounded-full p-4 glow-primary">
            <Play className="w-8 h-8 text-white fill-white" />
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <h3 className="text-white font-semibold text-lg">{title}</h3>
          {description && (
            <p className="text-white/80 text-xs mt-1">{description}</p>
          )}
        </div>
      </div>
    </DialogTrigger>
    <DialogContent className="max-w-4xl w-full bg-card border-border">
      <div className="aspect-video w-full">
        <iframe
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="w-full h-full rounded-lg"
        />
      </div>
    </DialogContent>
  </Dialog>
);

export default Fashion;
