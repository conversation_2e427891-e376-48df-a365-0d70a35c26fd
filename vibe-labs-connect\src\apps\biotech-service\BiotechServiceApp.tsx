
import { FlaskConical, ChevronRight, Code, Video, Microscope, Home, Users, Star, CheckCircle, Mail, Play, Eye } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ContactForm from "@/components/ContactForm";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import { FaWhatsapp } from "react-icons/fa";
import { HiOutlineMail } from "react-icons/hi";
import { useState } from "react";

const FloatingButtonGroup = ({ section }) => {
  return (
    <div className="fixed bottom-6 right-6 flex flex-col gap-3 z-50">
      <a
        href="https://wa.me/12494336588?text=Hello, I am reaching out to you from your website. I would like to know more about your biotech products."
        target="_blank"
        rel="noopener noreferrer"
        className="w-12 h-12 rounded-full bg-green-500 flex items-center justify-center shadow-lg hover:scale-110 transition float-animation"
        title="Chat on WhatsApp"
      >
        <img src="/favicon.ico" alt="WhatsApp" className="w-7 h-7" />
      </a>
      <a
        href="https://dashboard.tawk.to/"
        target="_blank"
        rel="noopener noreferrer"
        className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center shadow-lg hover:scale-110 transition float-animation"
        title="Chat on Tawk.to"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="white" className="w-7 h-7">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
        </svg>
      </a>
    </div>
  );
};

const biotechProductTechnicalProjects = [
  {
    title: "CRISPR Gene Editing Kit",
    description: "A complete kit for gene editing research, including reagents, protocols, and support.",
    tags: ["CRISPR", "Gene Editing", "Research Kit", "Protocols"],
    image: "photo-1465101046530-73398c7f28ca",
    liveUrl: "https://crispr-demo.vercel.app/"
  },
  {
    title: "Portable DNA Analyzer",
    description: "Handheld device for rapid DNA analysis in the field or lab.",
    tags: ["DNA Analysis", "Portable Device", "Rapid Testing", "Field Use"],
    image: "photo-1506744038136-46273834b3fb",
    liveUrl: "https://dnaanalyzer-demo.vercel.app/"
  },
  {
    title: "Biotech Consumables Marketplace",
    description: "Online marketplace for biotech consumables, reagents, and lab equipment.",
    tags: ["Marketplace", "Consumables", "Lab Equipment", "Reagents"],
    image: "photo-1515378791036-0648a3ef77b2",
    liveUrl: "https://biotechmarket-demo.vercel.app/"
  },
  {
    title: "IssyLabs Biotech Product Partner",
    description: "Collaboration with biotech product manufacturers to deliver innovative solutions.",
    tags: ["Partnership", "Innovation", "Product Development", "Support"],
    image: "photo-1465101178521-c1a4c8a0a8b7",
    liveUrl: "https://issylabsbiotechproductpartner.vercel.app/"
  }
];

const biotechProductCreativeProjects = [
  {
    title: "Product Demo Videos",
    description: "High-quality demo videos showcasing biotech products in action.",
    tags: ["Demo Videos", "Product Showcase", "Education", "Biotech"],
    image: "photo-1465101046530-73398c7f28ca",
    videoUrl: "dQw4w9WgXcQ"
  },
  {
    title: "Lab Equipment Tutorials",
    description: "Step-by-step video tutorials for using biotech lab equipment.",
    tags: ["Tutorials", "Lab Equipment", "How-To", "Biotech"],
    image: "photo-1506744038136-46273834b3fb",
    videoUrl: "3JZ_D3ELwOQ"
  },
  {
    title: "Product Launch Campaigns",
    description: "Media campaigns for new biotech product launches.",
    tags: ["Product Launch", "Media", "Campaign", "Biotech"],
    image: "photo-1515378791036-0648a3ef77b2",
    videoUrl: "L_jWHffIx5E"
  },
  {
    title: "IssyLabs Product Showcase",
    description: "Showcase of biotech product innovations and success stories.",
    tags: ["Showcase", "Innovation", "Success Stories", "Products"],
    image: "photo-1465101178521-c1a4c8a0a8b7",
    videoUrl: "eY52Zsg-KVI"
  }
];

const biotechProductTestimonials = [
  {
    name: "Dr. Alex Morgan",
    role: "Geneticist",
    content: "The CRISPR kit from IssyLabs made our gene editing research faster and more reliable.",
    rating: 5,
    image: "photo-1465101046530-73398c7f28ca"
  },
  {
    name: "Lisa Tran",
    role: "Lab Technician",
    content: "The portable DNA analyzer is a game changer for field testing.",
    rating: 5,
    image: "photo-1506744038136-46273834b3fb"
  },
  {
    name: "Samuel Lee",
    role: "Product Manager",
    content: "IssyLabs helped us launch our biotech consumables marketplace with stunning media and tech.",
    rating: 5,
    image: "photo-1515378791036-0648a3ef77b2"
  }
];

const VideoModal = ({ videoId, thumbnail, title, description }) => (
  <Dialog>
    <DialogTrigger asChild>
      <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
        <img 
          src={thumbnail} 
          alt={title}
          className="w-full h-64 object-cover"
        />
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
          <div className="bg-primary/90 rounded-full p-4 glow-primary">
            <Play className="w-8 h-8 text-white fill-white" />
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <h3 className="text-white font-semibold text-lg">{title}</h3>
          {description && (
            <p className="text-white/80 text-xs mt-1">{description}</p>
          )}
        </div>
      </div>
    </DialogTrigger>
    <DialogContent className="max-w-4xl w-full bg-card border-border">
      <div className="aspect-video w-full">
        <iframe
          src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
          title={title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="w-full h-full rounded-lg"
        />
      </div>
    </DialogContent>
  </Dialog>
);

const BiotechServiceApp = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [activeSection, setActiveSection] = useState("home");

  return (
    <div className="premium-gradient-bg min-h-screen text-white relative">
      {/* Glass Navbar */}
      <nav className="glass-effect premium-shadow flex items-center justify-between px-8 py-4 fixed top-0 left-0 w-full z-40 backdrop-blur-lg">
        <div className="flex items-center gap-3">
          <span className="text-2xl font-bold flex items-center gap-2">
            <span className="inline-block text-yellow-400">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M9 21V7a3 3 0 0 1 6 0v14" /><path d="M4 21v-7a3 3 0 0 1 6 0v7" /><path d="M20 21v-7a3 3 0 0 0-6 0v7" /></svg>
            </span>
            IssyLabs Biotech Service
          </span>
        </div>
        <div className="flex gap-6 text-lg">
          <a href="#home" className="hover:text-yellow-400 transition">Home</a>
          <a href="#about" className="hover:text-yellow-400 transition">About</a>
          <a href="#products" className="hover:text-yellow-400 transition">Products</a>
          <a href="#portfolio" className="hover:text-yellow-400 transition">Portfolio</a>
          <a href="#testimonials" className="hover:text-yellow-400 transition">Testimonials</a>
          <a href="#contact" className="hover:text-yellow-400 transition">Contact</a>
        </div>
        <a href="#start" className="ml-6 px-5 py-2 rounded-lg bg-yellow-400 text-gray-900 font-semibold shadow-lg hover-lift scale-in">Start Your Project</a>
      </nav>

      {/* Hero Section */}
      <section id="home" className="flex flex-col items-center justify-center min-h-screen pt-32 pb-16 text-center fade-in-up">
        <h1 className="text-5xl md:text-6xl font-extrabold mb-6">
          <span>Where Biotech Services</span>
          <br />
          <span className="text-yellow-400">Accelerate Innovation</span>
          <br />
          <span className="text-blue-400">and Science Delivers Results</span>
        </h1>
        <p className="text-xl md:text-2xl max-w-2xl mx-auto mb-8 text-gray-200">
          Discover, launch, and scale your biotech services with our unified technical and creative approach for biotech innovators.
        </p>
        <div className="flex gap-4 justify-center">
          <a href="#products" className="flex items-center gap-2 px-6 py-3 rounded-lg bg-yellow-400 text-gray-900 font-semibold shadow-lg hover-lift scale-in">
            <span className="inline-block">
              <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><circle cx="12" cy="12" r="10" /><path d="M15 12l-3-3-3 3" /></svg>
            </span>
            See Service Work
          </a>
          <a href="#start" className="flex items-center gap-2 px-6 py-3 rounded-lg bg-white text-blue-900 font-semibold shadow-lg hover-lift scale-in">
            <span className="inline-block">
              <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M10 20v-6h4v6" /><path d="M4 10V4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v6" /><path d="M12 16v-4" /></svg>
            </span>
            Start My Service Project
          </a>
        </div>
      </section>

      {/* Floating Chat Buttons */}
      <FloatingButtonGroup section={activeSection} />
    </div>

      {/* Products Section */}
      <section id="products" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Biotech Products
            </h2>
            <p className="text-xl text-muted-foreground">
              Explore our range hh of biotech products designed for innovation and research.
              Explore our range hh of biotech products designed for innovation and research.
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            {biotechProductTechnicalProjects.map((project, index) => (
              <Card 
                key={index} 
                className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
                style={{animationDelay: `${index * 0.1}s`}}
                onClick={() => window.open(project.liveUrl, '_blank')}
              >
                <CardContent className="p-0">
                  <div 
                    className="h-64 relative overflow-hidden rounded-t-lg"
                    style={{
                      backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                      {project.title}
                    </h4>
                    <p className="text-muted-foreground mb-4 text-sm">
                      {project.description}
                    </p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.tags.map((tag, i) => (
                        <span key={i} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold">
                          {tag}
                        </span>
                      ))}
                    </div>
                    <Button 
                      size="sm" 
                      className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
                      onClick={() => window.open(project.liveUrl, '_blank')}
                    >
                      View Product
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Product Media & Campaigns
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns—see how we deliver complete solutions for biotech products.
            </p>
          </div>
          {/* Creative Biotech Product Projects */}
          <div className="grid md:grid-cols-2 gap-8">
            {biotechProductCreativeProjects.map((project, index) => (
              <Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-0">
                  <VideoModal
                    videoId={project.videoUrl}
                    thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                    title={project.title}
                    description={project.description}
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Product Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from biotech product professionals we've helped transform their business.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {biotechProductTestimonials.map((testimonial, index) => (
              <Card key={index} className="glass-effect premium-shadow hover-lift scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{backgroundColor: '#0a1433'}}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">Ready to Transform Your Product Business?</h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{animationDelay: '0.2s'}}>Let's build the perfect tech and creative strategy for your biotech product business.</p>
          </div>
          <div className="fade-in-up" style={{animationDelay: '0.4s'}}>
            <ContactForm 
              pageSource="Biotech Product Page"
              businessTypes={["Gene Editing","DNA Analysis","Lab Equipment","Product Development","Biotech Marketplace","Other"]}
            />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <FlaskConical className="h-6 w-6 text-accent mr-2" />
                IssyLabs
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Biotech Product Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner IssyVibe Production.
              </p>
              <br />
              <div className="space-y-4 text-3xl font-bold">
                {/* Email */}
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
                >
                  <HiOutlineMail className="text-gray-500" />
                  <span><EMAIL></span>
                </a>
                <br/>
                {/* WhatsApp */}
                <a
                  href="https://wa.me/12494336588"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 text-green-600 hover:underline"
                >
                  <FaWhatsapp className="text-green-500" />
                  <span>+1 (249) 433‑6588‬</span>
                </a>
              </div>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link to="/" className="text-muted-foreground hover:text-accent transition-colors">Home</Link></li>
                <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                <li><a href="#products" className="text-muted-foreground hover:text-accent transition-colors">Products</a></li>
                <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li><Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</Link></li>
                <li><Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</Link></li>
                <li><Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</Link></li>
                <li><Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</Link></li>
                <li><Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</Link></li>
                <li><Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</Link></li>
                <li><Link to="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</Link></li>
                <li><Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2026 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BiotechServiceApp;
