<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>IssyLabs + IssyVibe Production - Your All-in-One Digital & Media Brand System</title>
    <meta name="description" content="Where Technology Empowers and Storytelling Converts. Complete digital solutions and creative production for businesses across all industries." />
    <meta name="author" content="IssyLabs + IssyVibe Production" />

    <meta property="og:title" content="IssyLabs + IssyVibe Production - Digital & Media Solutions" />
    <meta property="og:description" content="Launch better. Scale smarter. Build something remarkable with our technical and creative expertise." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="vibe-labs-connect/image/issylabsimage.jpg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@issylabs" />
    <meta name="twitter:image" content="vibe-labs-connect/image/issylabsimage.jpg" />
    <link rel="icon" type="image/png" href="/image/cropped_circle_image.png" style="border-radius:50%" />

    <!-- ✅ Plausible Analytics Tracking Script -->
    <script defer data-domain="issylabs.vercel.app" src="https://plausible.io/js/script.file-downloads.hash.outbound-links.pageview-props.revenue.tagged-events.js"></script>
    <script>
      window.plausible = window.plausible || function () {
        (window.plausible.q = window.plausible.q || []).push(arguments);
      };
    </script>
    <!-- End Plausible Analytics -->
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
      var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
      (function(){
        var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        s1.async=true;
        s1.src='https://embed.tawk.to/6881751f31ae371913c577a5/1j0sr31b3';
        s1.charset='UTF-8';
        s1.setAttribute('crossorigin','*');
        s0.parentNode.insertBefore(s1,s0);
      })();
    </script>
    <!--End of Tawk.to Script-->
  </body>
</html>
