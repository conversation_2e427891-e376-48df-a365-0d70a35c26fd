import { Building, Heart, Users, Star, CheckCircle, Code, Video, Calendar, Mail, Phone, Sparkles, Eye, ChevronRight, Home, Camera, Play, MessageCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON> } from "react-router-dom";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import ContactForm from "@/components/ContactForm";
import { useState } from "react";

// Floating WhatsApp Button (same as home)
const FloatingButtonGroup = ({ section }) => {
  const phone = '12494336588';
  const message = encodeURIComponent(`Hello, I am reaching out to you from your Healthcare page. I would like to know more about your services.`);
  const whatsappUrl = `https://wa.me/${phone}?text=${message}`;
  return (
    <div className="fixed bottom-5 right-32 z-50 flex flex-col items-end gap-4">
      <a
        href={whatsappUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="rounded-full bg-green-500 hover:bg-green-600 shadow-lg w-14 h-14 flex items-center justify-center transition-all"
        aria-label="Chat on WhatsApp"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.967-.94 1.166-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.1 3.2 5.077 4.363.71.306 1.263.489 1.695.626.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/><path fill="currentColor" fillRule="evenodd" d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.34 4.997L2.003 22l5.137-1.343c1.462.8 3.09 1.222 4.864 1.222 5.514 0 9.997-4.483 9.997-9.997 0-2.668-1.04-5.175-2.927-7.062-1.888-1.888-4.395-2.927-7.062-2.927zm-8.001 9.997c0-4.418 3.583-8.001 8.001-8.001 2.137 0 4.146.832 5.656 2.343 1.511 1.51 2.343 3.519 2.343 5.656 0 4.418-3.583 8.001-8.001 8.001-1.548 0-3.04-.44-4.32-1.272l-.307-.19-3.057.799.82-2.995-.2-.314C3.44 15.04 3.003 13.548 3.003 12z" clipRule="evenodd"/></svg>
      </a>
    </div>
  );
};

const healthcareTechnicalProjects = [
  {
    title: "Patient Portal Platform",
    description: "Secure patient login, appointment booking, and health record management.",
    image: "photo-1519494026892-80bbd2d6fd23",
    liveUrl: "#",
    tags: ["Portal", "Booking", "Records"],
  },
  {
    title: "Telehealth Video Consultations",
    description: "HIPAA-compliant video calls and chat for remote healthcare.",
    image: "photo-1465101046530-73398c7f28ca",
    liveUrl: "#",
    tags: ["Telehealth", "Video", "Chat"],
  },
  {
    title: "Healthcare CRM Dashboard",
    description: "Manage patient relationships, reminders, and follow-ups.",
    image: "photo-1515165562835-cf7747c1e9fb",
    liveUrl: "#",
    tags: ["CRM", "Reminders", "Dashboard"],
  },
];
const healthcareCreativeProjects = [
  {
    title: "Patient Success Stories",
    description: "Video testimonials and stories from real patients.",
    image: "photo-1518611012118-696072aa579a",
    tags: ["Video", "Testimonial", "Branding"],
    videoUrl: "Frubc3nGqWA",
  },
  {
    title: "Doctor Introduction Videos",
    description: "Professional videos introducing your healthcare team.",
    image: "photo-1607962837359-5e7e89f86776",
    videoUrl: "PkL17htzkHg",
  },
];
const healthcareTestimonials = [
  {
    name: "Dr. Jane Smith",
    role: "Clinic Owner",
    content:
      "IssyLabs helped us launch a secure patient portal and create compelling patient story videos. Our clinic is more efficient and trusted!",
    image: "photo-1678875524808-78e1d24b6597",
    rating: 5,
  },
  {
    name: "Michael O.",
    role: "Practice Manager",
    content:
      "The telehealth system and CRM dashboard have transformed our patient experience. Highly recommended!",
    image: "photo-1506744038136-46273834b3fb",
    rating: 5,
  },
];
const businessTypes = [
  "Clinic Owner",
  "Doctor",
  "Practice Manager",
  "Nurse",
  "Therapist",
  "Healthcare Admin",
  "Other",
];

const Healthcare = () => {
  const [activeSection, setActiveSection] = useState("home");
  return (
    <div className="min-h-screen premium-gradient-bg">
      <FloatingButtonGroup section={activeSection} />
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Heart className="h-6 w-6 text-accent mr-2" />
              IssyLabs Healthcare
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              {["home", "about", "services", "portfolio", "testimonials", "contact"].map((item) => (
                <a
                  key={item}
                  href={`#${item}`}
                  className="text-muted-foreground hover:text-accent transition-colors font-medium"
                >
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </a>
              ))}
            </div>
            <Button
              onClick={() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })}
              className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
            >
              Start Your Project
            </Button>
          </div>
        </div>
      </nav>
      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1519494026892-80bbd2d6fd23?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        ></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Where Healthcare
              <br />
              <span className="text-accent float-animation inline-block">Cares Smarter</span>
              <br />
              and Technology
              <br />
              <span className="text-primary float-animation inline-block" style={{ animationDelay: "1s" }}>
                Powers Trust
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
              Launch your clinic. Scale your care. Build something trusted with our unified technical and creative approach for healthcare professionals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                onClick={() => document.getElementById("portfolio")?.scrollIntoView({ behavior: "smooth" })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Healthcare Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Home className="mr-2 h-5 w-5" />
                Start My Healthcare Project
              </Button>
            </div>
          </div>
        </div>
      </section>
      {/* About Section */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              One Team. Two Forces. Unified Healthcare Execution.
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
              IssyLabs combines technical excellence with creative storytelling from our partner IssyVibes Production to deliver complete digital solutions for clinics and healthcare brands.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12">
            {/* IssyLabs for Healthcare */}
            <Card className="hover-lift glass-effect premium-shadow scale-in">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-primary/20 mr-4">
                    <Code className="h-10 w-10 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground">Healthcare Tech Solutions</h3>
                    <p className="text-muted-foreground">IssyLabs for Healthcare</p>
                  </div>
                </div>
                <p className="text-muted-foreground mb-6">
                  Your digital backbone for speed, structure, and trust in healthcare.
                </p>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Patient management & automation
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Appointment booking & scheduling
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Secure payment & billing systems
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Health record dashboards
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Branded website & patient portal
                  </li>
                </ul>
              </CardContent>
            </Card>
            {/* IssyVibe for Healthcare */}
            <Card className="hover-lift glass-effect premium-shadow scale-in" style={{animationDelay: '0.2s'}}>
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-accent/20 mr-4">
                    <Video className="h-10 w-10 text-accent" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground">Healthcare Visual Production</h3>
                    <p className="text-muted-foreground">IssyVibe for Healthcare</p>
                  </div>
                </div>
                <p className="text-muted-foreground mb-6">
                  Creative services powered by our in-house partner bringing your clinic to life through compelling visuals and content.
                </p>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Patient stories & testimonials
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Doctor introduction videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Social media content & reels
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Promotional & marketing videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Team branding & interviews
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Technical Solutions + Creative Media
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to launch, scale, and succeed in healthcare.
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Technical Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-left">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Code className="h-10 w-10 text-primary" />
                  <span className="ml-4 text-xl font-bold text-foreground">Healthcare Tech</span>
                </div>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Patient management
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Appointment booking & scheduling
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Secure payment & billing automation
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Health record dashboards
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Branded website & patient portal
                  </li>
                </ul>
              </CardContent>
            </Card>
            {/* Creative Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-right">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <Camera className="h-10 w-10 text-accent" />
                  <span className="ml-4 text-xl font-bold text-foreground">Healthcare Media</span>
                </div>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Patient stories & testimonials
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Doctor introduction videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Social media content & reels
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Promotional & marketing videos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Team branding & interviews
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Healthcare Projects We've Delivered
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative campaigns — see how we deliver complete solutions for healthcare brands.
            </p>
          </div>
          {/* Technical Healthcare Projects */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <Code className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Healthcare Tech Solutions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {healthcareTechnicalProjects.map((project, index) => (
                <Card
                  key={index}
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                  onClick={() => window.open(project.liveUrl, "_blank")}
                >
                  <CardContent className="p-0">
                    <div
                      className="h-64 relative overflow-hidden rounded-t-lg"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    </div>
                    <div className="p-6">
                      <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-accent transition-colors">
                        {project.title}
                      </h4>
                      <p className="text-muted-foreground mb-4 text-sm">
                        {project.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, i) => (
                          <span
                            key={i}
                            className="bg-primary/10 text-primary px-3 py-1 rounded-full text-xs font-semibold"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <Button
                        size="sm"
                        className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
                        onClick={() => window.open(project.liveUrl, "_blank")}
                      >
                        View Project
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          {/* Creative Healthcare Projects */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <Video className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Healthcare Visual Productions</h3>
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {healthcareCreativeProjects.map((project, index) => (
                <Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                  <CardContent className="p-0">
                    <Dialog>
                      <DialogTrigger asChild>
                        <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
                          <img src={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`} alt={project.title} className="w-full h-64 object-cover" />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                            <div className="bg-primary/90 rounded-full p-4 glow-primary">
                              <Play className="w-8 h-8 text-white fill-white" />
                            </div>
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                            <h3 className="text-white font-semibold text-lg">{project.title}</h3>
                            <p className="text-white text-sm">{project.description}</p>
                          </div>
                        </div>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl w-full bg-card border-border">
                        <div className="aspect-video w-full">
                          <iframe src={`https://www.youtube.com/embed/${project.videoUrl}?autoplay=1`} title={project.title} allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen className="w-full h-full rounded-lg" />
                        </div>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl font-bold text-foreground mb-6">
              Healthcare Success Stories
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from healthcare professionals we've helped transform their business.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {healthcareTestimonials.map((testimonial, index) => (
              <Card key={index} className="glass-effect premium-shadow hover-lift scale-in" style={{ animationDelay: `${index * 0.1}s` }}>
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`,
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground italic mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <p className="font-semibold text-foreground">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Contact Section */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: "#0a1433" }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">
              Ready to Transform Your Healthcare Business?
            </h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{ animationDelay: "0.2s" }}>
              Let's build the perfect tech and creative strategy for your clinic or practice.
            </p>
          </div>
          <div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
            <ContactForm
              pageSource="Healthcare Page"
              businessTypes={businessTypes}
            />
          </div>
        </div>
      </section>
      {/* Footer */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Heart className="h-6 w-6 text-accent mr-2" />
                IssyLabs Healthcare
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Healthcare Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner IssyVibe Production.
              </p>
              <br></br>
              <p className="text-muted-foreground/80 size-full text-4xl font-bold ">
                <EMAIL>
              </p>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li>
                  <Link to="/" className="text-muted-foreground hover:text-accent transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <a href="#about" className="text-muted-foreground hover:text-accent transition-colors">
                    About
                  </a>
                </li>
                <li>
                  <a href="#services" className="text-muted-foreground hover:text-accent transition-colors">
                    Services
                  </a>
                </li>
                <li>
                  <a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">
                    Portfolio
                  </a>
                </li>
                <li>
                  <a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">
                    Testimonials
                  </a>
                </li>
                <li>
                  <a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li>
                  <Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">
                    Fashion
                  </Link>
                </li>
                <li>
                  <Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">
                    Real Estate
                  </Link>
                </li>
                <li>
                  <Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">
                    Music
                  </Link>
                </li>
                <li>
                  <Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">
                    Coaching
                  </Link>
                </li>
                <li>
                  <Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">
                    Video Creation
                  </Link>
                </li>
                <li>
                  <Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">
                    Automation
                  </Link>
                </li>
                <li>
                  <Link to="/healthcare" className="text-muted-foreground hover:text-accent transition-colors">
                    Healthcare
                  </Link>
                </li>
                <li>
                  <Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">
                    Partner Services
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Healthcare;
export const FloatingWhatsAppButton = () => {
  return <FloatingButtonGroup section="home" />;
};