
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium Design System with Deep Blue Gradients */

@layer base {
  :root {
    --background: 222 47% 7%;
    --foreground: 210 40% 98%;

    --card: 222 47% 9%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 45 93% 47%;
    --accent-foreground: 222 47% 7%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 221 83% 53%;

    --radius: 0.75rem;

    --sidebar-background: 222 47% 7%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 221 83% 53%;
  }

  .dark {
    --background: 222 47% 7%;
    --foreground: 210 40% 98%;

    --card: 222 47% 9%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 9%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 45 93% 47%;
    --accent-foreground: 222 47% 7%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 221 83% 53%;
    
    --sidebar-background: 222 47% 7%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 221 83% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Premium Animations and Effects */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px hsl(var(--primary));
  }
  50% {
    box-shadow: 0 0 20px hsl(var(--primary)), 0 0 30px hsl(var(--primary));
  }
  100% {
    box-shadow: 0 0 5px hsl(var(--primary));
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-left {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.premium-gradient-bg {
  background: linear-gradient(135deg, 
    hsl(222 47% 7%) 0%, 
    hsl(230 47% 12%) 25%, 
    hsl(238 47% 15%) 50%, 
    hsl(245 47% 12%) 75%, 
    hsl(222 47% 7%) 100%);
  background-size: 400% 400%;
  animation: gradient-shift 20s ease infinite;
}

.hero-gradient {
  background: linear-gradient(135deg, 
    hsl(222 47% 7%) 0%, 
    hsl(230 83% 20%) 30%, 
    hsl(245 83% 25%) 70%, 
    hsl(222 47% 7%) 100%);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

.fade-in-left {
  animation: fade-in-left 0.8s ease-out;
}

.fade-in-right {
  animation: fade-in-right 0.8s ease-out;
}

.scale-in {
  animation: scale-in 0.6s ease-out;
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.premium-shadow {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

html {
  scroll-behavior: smooth;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--accent));
}
