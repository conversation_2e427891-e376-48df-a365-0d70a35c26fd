import { useState } from "react";
import { <PERSON>rkles, TrendingUp, Users, Star, CheckCircle, Code, Video, Calendar, Mail, Phone, ChevronRight, ExternalLink, ArrowLeft, Music as MusicIcon, Play } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import ContactForm from "@/components/ContactForm";
import YouTubeEmbed from "@/components/YouTubeEmbed";
import { DialogContent, Dialog, DialogTrigger } from "@/components/ui/dialog";

const musicTechnicalProjects = [
	{
		title: "Artist Platform & Streaming",
		description:
			"Custom music streaming, fan management, and analytics for artists and labels.",
		image: "photo-1493225457124-a3eb161ffa5f",
		liveUrl: "https://example-music-platform.com",
		tags: [
			"Streaming",
			"Fan Management",
			"Analytics",
			"Artist Platform",
		],
	},
	{
		title: "Music Booking & Event System",
		description:
			"Automated booking, event scheduling, and ticketing for musicians and venues.",
		image: "photo-1461749280684-dccba630e2f6",
		liveUrl: "https://example-music-booking.com",
		tags: ["Booking", "Events", "Ticketing", "Calendar"],
	},
];
const musicCreativeProjects = [
	{
		title: "Music Video Production",
		description: "Cinematic music videos and visual content for artists.",
		image: "photo-1518611012118-696072aa579a",
		videoUrl: "dQw4w9WgXcQ",
	},
	{
		title: "Album Launch Campaigns",
		description:
			"Multi-platform promotion, fan engagement, and social media reels.",
		image: "photo-1464983953574-0892a716854b",
		videoUrl: "dQw4w9WgXcQ",
	},
];
const musicTestimonials = [
	{
		name: "Jade S.",
		role: "Solo Artist",
		content:
			"IssyLabs built my streaming platform and produced my music videos. My fanbase and engagement exploded!",
		image: "photo-1493225457124-a3eb161ffa5f",
		rating: 5,
	},
	{
		name: "The Sound Collective",
		role: "Band",
		content:
			"Our booking and event system is seamless, and the video content is top-notch. Highly recommend!",
		image: "photo-1461749280684-dccba630e2f6",
		rating: 5,
	},
	{
		name: "DJ Nova",
		role: "Producer/DJ",
		content:
			"Professional, creative, and reliable. IssyLabs is my go-to for tech and media solutions.",
		image: "photo-1518611012118-696072aa579a",
		rating: 5,
	},
];
const businessTypes = [
	"Solo Artist",
	"Band",
	"Producer/DJ",
	"Music Label",
	"Songwriter",
	"Other",
];

const Music = () => {
	const [activeTab, setActiveTab] = useState<"all" | "menu">("all");

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
			{/* Navigation */}
			<nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center py-4">
						<div className="text-2xl font-bold text-white flex items-center">
							<MusicIcon className="h-6 w-6 text-amber-400 mr-2" />
							IssyLabs Music
						</div>
						{/* Desktop Navigation */}
						<div className="hidden md:flex space-x-8">
							{["home","about","services","portfolio","testimonials","contact"].map((item) => (
								<button
									key={item}
									onClick={() => document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' })}
									className="text-sm font-medium transition-all duration-300 hover:text-amber-400 text-slate-400"
								>
									{item.charAt(0).toUpperCase() + item.slice(1)}
								</button>
							))}
						</div>
						{/* Mobile Menu Button */}
						<Button
							className="md:hidden text-white"
							onClick={() => setActiveTab(activeTab === "menu" ? "all" : "menu")}
						>
							{activeTab === "menu" ? <Sparkles className="h-6 w-6" /> : <MusicIcon className="h-6 w-6" />}
						</Button>
						{/* CTA Button */}
						<Button 
							onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
							className="hidden md:flex bg-amber-400 hover:bg-amber-500 text-slate-900 font-semibold shadow-lg hover-lift pulse-glow"
						>
							Start Your Project
						</Button>
					</div>
					{/* Mobile Navigation */}
					{activeTab === "menu" && (
						<div className="md:hidden py-4 border-t border-slate-700 glass-effect">
							{["home","about","services","portfolio","testimonials","contact"].map((item) => (
								<button
									key={item}
									onClick={() => {document.getElementById(item)?.scrollIntoView({ behavior: 'smooth' }); setActiveTab("all")}}
									className="block w-full text-left py-2 text-slate-400 hover:text-amber-400"
								>
									{item.charAt(0).toUpperCase() + item.slice(1)}
								</button>
							))}
						</div>
					)}
				</div>
			</nav>
			{/* Back to Home Button - Centered Below Navbar */}
			

			{/* Hero Section */}
			<section
				id="home"
				className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden"
			>
				<div className="absolute inset-0 opacity-20"
					style={{
						backgroundImage: `url(https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
						backgroundSize: 'cover',
						backgroundPosition: 'center'
					}}
				></div>
				<div className="absolute inset-0 bg-gradient-to-br from-black/40 to-transparent"></div>
				<div className="max-w-7xl mx-auto text-center relative z-10">
					<div className="fade-in-up">
						<h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
							Where Music
							<br />
							<span className="text-amber-400 float-animation inline-block">Inspires</span>
							<br />
							and Technology
							<br />
							<span className="text-purple-400 float-animation inline-block" style={{animationDelay: '1s'}}>
								Delivers
							</span>
						</h1>
						<p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90 text-white/90">
							Launch your sound. Grow your fanbase. Build something iconic with our unified technical and creative approach for music brands and artists.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Button 
								size="lg" 
								onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
								className="bg-amber-400 text-slate-900 hover:bg-amber-500 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
							>
								<Sparkles className="mr-2 h-5 w-5" />
								See Music Work
								<ChevronRight className="ml-2 h-5 w-5" />
							</Button>
							<Button 
								size="lg" 
								variant="outline" 
								onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
								className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-slate-900 px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
							>
								<Calendar className="mr-2 h-5 w-5" />
								Start My Music Project
							</Button>
						</div>
					</div>
				</div>
			</section>

			{/* About Section - Music Focused */}
			<section
				id="about"
				className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50"
			>
				<div className="max-w-7xl mx-auto text-center">
					<div className="fade-in-up">
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
							One Team. Two Forces. Unified Music Execution.
						</h2>
						<p className="text-xl text-slate-300 mb-16 max-w-3xl mx-auto">
							IssyLabs combines technical excellence with creative storytelling to deliver complete digital solutions for music brands and artists.
						</p>
					</div>
					<div className="grid lg:grid-cols-2 gap-12">
						{/* IssyLabs for Music */}
						<Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 hover-lift premium-shadow scale-in">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<div className="p-3 rounded-full bg-blue-400/20 mr-4">
										<Code className="h-10 w-10 text-blue-400" />
									</div>
									<div>
										<h3 className="text-2xl font-bold text-white">Music Tech Solutions</h3>
										<p className="text-slate-300">IssyLabs for Music</p>
									</div>
								</div>
								<p className="text-slate-300 mb-6">
									Your digital backbone for speed, structure, and growth in music.
								</p>
								<ul className="space-y-3 text-slate-300">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Streaming platforms & artist sites
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Booking & event systems
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Fan management & analytics
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Automated ticketing & scheduling
									</li>
								</ul>
							</CardContent>
						</Card>
						{/* IssyVibe for Music */}
						<Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 hover-lift premium-shadow scale-in" style={{animationDelay: '0.2s'}}>
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<div className="p-3 rounded-full bg-purple-400/20 mr-4">
										<Video className="h-10 w-10 text-purple-400" />
									</div>
									<div>
										<h3 className="text-2xl font-bold text-white">Music Visual Production</h3>
										<p className="text-slate-300">IssyVibe for Music</p>
									</div>
								</div>
								<p className="text-slate-300 mb-6">
									Creative services powered by our in-house partner — bringing your music to life through compelling visuals and content.
								</p>
								<ul className="space-y-3 text-slate-300">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Music video production
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Album launch campaigns
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Social media reels & promo content
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Artist branding & interviews
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>

			{/* Services Section */}
			<section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
							Technical Solutions + Creative Media
						</h2>
						<p className="text-xl text-slate-300">
							Everything you need to launch, scale, and succeed in music.
						</p>
					</div>
					<div className="grid lg:grid-cols-2 gap-12 mb-20">
						<Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Code className="h-10 w-10 text-blue-400 mr-4" />
									<span className="ml-4 text-xl font-bold text-white">
										Music Tech
									</span>
								</div>
								<ul className="space-y-3 text-slate-300">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Streaming platforms & artist sites
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Booking & event systems
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Fan management & analytics
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-blue-400 mr-2" />
										Automated ticketing & scheduling
									</li>
								</ul>
							</CardContent>
						</Card>
						<Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
							<CardContent className="p-8">
								<div className="flex items-center mb-6">
									<Video className="h-10 w-10 text-purple-400 mr-4" />
									<span className="ml-4 text-xl font-bold text-white">
										Music Media
									</span>
								</div>
								<ul className="space-y-3 text-slate-300">
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Music video production
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Album launch campaigns
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Social media reels & promo content
									</li>
									<li className="flex items-center">
										<CheckCircle className="h-4 w-4 text-purple-400 mr-2" />
										Artist branding & interviews
									</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</section>
			<section
				id="portfolio"
				className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30"
			>
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl font-bold text-white mb-6">
							Music Projects We've Launched
						</h2>
						<p className="text-xl text-slate-300">
							Technical excellence meets creative campaigns — see how we deliver complete solutions for music brands.
						</p>
					</div>
					{/* Technical Music Projects */}
					<div className="mb-20">
						<div className="flex items-center mb-12">
							<div className="p-3 rounded-full bg-amber-400/20 mr-4">
								<Code className="h-8 w-8 text-amber-400" />
							</div>
							<h3 className="text-3xl font-bold text-white">Music Tech Solutions</h3>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							{musicTechnicalProjects.map((project, index) => (
								<Card
									key={index}
									className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
									style={{ animationDelay: `${index * 0.1}s` }}
									onClick={() => window.open(project.liveUrl, "_blank")}
								>
									<CardContent className="p-0">
										<div
											className="h-64 relative overflow-hidden rounded-t-lg"
											style={{
												backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
												backgroundSize: "cover",
												backgroundPosition: "center",
											}}
										>
											<div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
										</div>
										<div className="p-6">
											<h4 className="text-xl font-semibold text-white mb-2 group-hover:text-amber-400 transition-colors">
												{project.title}
											</h4>
											<p className="text-slate-300 mb-4 text-sm">
												{project.description}
											</p>
											<div className="flex flex-wrap gap-2 mb-4">
												{project.tags.map((tag, i) => (
													<span
														key={i}
														className="bg-amber-400/10 text-amber-400 px-3 py-1 rounded-full text-xs font-semibold"
													>
														{tag}
													</span>
												))}
											</div>
											<Button
												size="sm"
												className="bg-amber-400 text-slate-900 hover:bg-amber-500 font-semibold px-4 py-2 text-sm shadow-md hover-lift"
												onClick={() => window.open(project.liveUrl, "_blank")}
											>
												View Project
												<ChevronRight className="ml-2 h-4 w-4" />
											</Button>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
					{/* Creative Music Projects */}
					<div>
						<div className="flex items-center mb-12">
							<div className="p-3 rounded-full bg-purple-400/20 mr-4">
								<MusicIcon className="h-8 w-8 text-purple-400" />
							</div>
							<h3 className="text-3xl font-bold text-white">Music Visual Productions</h3>
						</div>
						<div className="grid md:grid-cols-2 gap-8">
							{musicCreativeProjects.map((project, index) => (
								<Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
									<CardContent className="p-0">
										<VideoModal
											videoId={project.videoUrl}
											thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
											title={project.title}
											description={project.description}
										/>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Testimonials Section */}
			<section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16 fade-in-up">
						<h2 className="text-4xl font-bold text-white mb-6">
							Music Success Stories
						</h2>
						<p className="text-xl text-slate-300">
							Real results from music professionals we've helped transform their
							careers.
						</p>
					</div>
					<div className="grid md:grid-cols-3 gap-8">
						{musicTestimonials.map((testimonial, index) => (
							<Card
								key={index}
								className="bg-slate-800/50 border-slate-700 hover-lift scale-in"
								style={{ animationDelay: `${index * 0.1}s` }}
							>
								<CardContent className="p-8">
									<div className="flex items-center mb-4">
										<div
											className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
											style={{
												backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`,
											}}
										></div>
										<div className="flex">
											{[...Array(testimonial.rating)].map((_, i) => (
												<Star
													key={i}
													className="h-4 w-4 text-amber-400 fill-current"
												/>
											))}
										</div>
									</div>
									<p className="text-slate-300 italic mb-6">
										"{testimonial.content}"
									</p>
									<div>
										<p className="font-semibold text-white">
											{testimonial.name}
										</p>
										<p className="text-sm text-amber-400">
											{testimonial.role}
										</p>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			</section>

			{/* Contact Section - Deep Blue Background Only */}
			<section
				id="contact"
				className="py-20 px-4 sm:px-6 lg:px-8"
				style={{ backgroundColor: "#0a1433" }}
			>
				<div className="max-w-7xl mx-auto">
					<div className="text-center mb-16">
						<h2 className="text-4xl font-bold text-white mb-6 fade-in-up">
							Ready to Amplify Your Music Career?
						</h2>
						<p className="text-xl text-slate-300 fade-in-up" style={{ animationDelay: "0.2s" }}>
							Let's build the perfect tech and creative strategy for your music
							brand.
						</p>
					</div>
					<div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
						<ContactForm
							pageSource="Music Page"
							businessTypes={businessTypes}
						/>
					</div>
				</div>
			</section>
			<footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
				<div className="max-w-7xl mx-auto">
					<div className="grid md:grid-cols-4 gap-8">
						<div className="md:col-span-2">
							<div className="text-2xl font-bold text-white mb-4 flex items-center">
								<MusicIcon className="h-6 w-6 text-amber-400 mr-2" />
								IssyLabs
							</div>
							<p className="text-slate-300 mb-4">
								Your All-in-One Music Tech & Media Brand System
							</p>
							<p className="text-slate-400 mb-4">
								Creative services are powered by our in-house partner — IssyVibe
								Production.
							</p>
							<p className="text-slate-400"><EMAIL></p>
						</div>
						<div>
							<h4 className="text-white font-semibold mb-4">Quick Links</h4>
							<ul className="space-y-2">
								<li>
									<Link
										to="/"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Home
									</Link>
								</li>
								<li>
									<a
										href="#about"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										About
									</a>
								</li>
								<li>
									<a
										href="#services"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Services
									</a>
								</li>
								<li>
									<a
										href="#portfolio"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Portfolio
									</a>
								</li>
								<li>
									<a
										href="#testimonials"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Testimonials
									</a>
								</li>
								<li>
									<a
										href="#contact"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Contact
									</a>
								</li>
							</ul>
						</div>
						<div>
							<h4 className="text-white font-semibold mb-4">Industries</h4>
							<ul className="space-y-2">
								<li>
									<Link
										to="/fashion"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Fashion
									</Link>
								</li>
								<li>
									<Link
										to="/realestate"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Real Estate
									</Link>
								</li>
								<li>
									<Link
										to="/music"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Music
									</Link>
								</li>
								<li>
									<Link
										to="/coaching"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Coaching
									</Link>
								</li>
								<li>
									<Link
										to="/video"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Video Creation
									</Link>
								</li>
								<li>
									<Link
										to="/automation"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Automation
									</Link>
								</li>
								<li>
									<Link
										to="/gym"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Gym & Fitness
									</Link>
								</li>
								<li>
									<Link
										to="/partner"
										className="text-slate-400 hover:text-amber-400 transition-colors"
									>
										Partner Services
									</Link>
								</li>
							</ul>
						</div>
					</div>
					<div className="border-t border-slate-700 mt-8 pt-8 text-center text-slate-400">
						<p>&copy; 2025 IssyLabs. All rights reserved.</p>
					</div>
				</div>
			</footer>
		</div>
	);
};

// VideoModal for creative projects
const VideoModal = ({ videoId, thumbnail, title, description }) => (
	<Dialog>
		<DialogTrigger asChild>
			<div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
				<img 
					src={thumbnail} 
					alt={title}
					className="w-full h-48 object-cover"
				/>
				<div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
					<div className="bg-primary/90 rounded-full p-4 glow-primary">
						<Play className="w-8 h-8 text-white fill-white" />
					</div>
				</div>
				<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
					<h3 className="text-white font-semibold text-sm">{title}</h3>
					{description && (
						<p className="text-white/80 text-xs mt-1">{description}</p>
					)}
				</div>
			</div>
		</DialogTrigger>
		<DialogContent className="max-w-4xl w-full bg-card border-border">
			<div className="aspect-video w-full">
				<iframe
					src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
					title={title}
					allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
					allowFullScreen
					className="w-full h-full rounded-lg"
				/>
			</div>
		</DialogContent>
	</Dialog>
);

export default Music;
