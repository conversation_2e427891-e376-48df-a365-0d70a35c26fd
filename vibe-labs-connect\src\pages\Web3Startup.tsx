import { Building, Heart, Users, Star, CheckCircle, Code, Video, Calendar, Mail, Phone, Sparkles, Eye, ChevronRight, Home, Camera, Play, MessageCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON> } from "react-router-dom";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import ContactForm from "@/components/ContactForm";
import { useState } from "react";

// Floating WhatsApp Button (same as home)
const FloatingButtonGroup = ({ section }) => {
  const phone = '12494336588';
  const message = encodeURIComponent(`Hello, I am reaching out to you from your Web3 Startups page. I would like to know more about your services.`);
  const whatsappUrl = `https://wa.me/${phone}?text=${message}`;
  return (
    <div className="fixed bottom-5 right-32 z-50 flex flex-col items-end gap-4">
      <a
        href={whatsappUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="rounded-full bg-green-500 hover:bg-green-600 shadow-lg w-14 h-14 flex items-center justify-center transition-all"
        aria-label="Chat on WhatsApp"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.967-.94 1.166-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.1 3.2 5.077 4.363.71.306 1.263.489 1.695.626.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/><path fill="currentColor" fillRule="evenodd" d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.34 4.997L2.003 22l5.137-1.343c1.462.8 3.09 1.222 4.864 1.222 5.514 0 9.997-4.483 9.997-9.997 0-2.668-1.04-5.175-2.927-7.062-1.888-1.888-4.395-2.927-7.062-2.927zm-8.001 9.997c0-4.418 3.583-8.001 8.001-8.001 2.137 0 4.146.832 5.656 2.343 1.511 1.51 2.343 3.519 2.343 5.656 0 4.418-3.583 8.001-8.001 8.001-1.548 0-3.04-.44-4.32-1.272l-.307-.19-3.057.799.82-2.995-.2-.314C3.44 15.04 3.003 13.548 3.003 12z" clipRule="evenodd"/></svg>
      </a>
    </div>
  );
};

const Web3Startup = () => {
  const [activeSection, setActiveSection] = useState("home");
  return (
    <div className="min-h-screen premium-gradient-bg">
      <FloatingButtonGroup section={activeSection} />
      {/* ... You can copy the Healthcare page structure and adjust content for Web3 Startups ... */}
      <div className="text-center pt-32 pb-20">
        <h1 className="text-5xl font-bold mb-6">Web3 Startup Companies</h1>
        <p className="text-xl mb-8">Empowering the next generation of decentralized innovation with IssyLabs technical and creative solutions.</p>
        <Button className="bg-accent text-accent-foreground font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow">
          Start Your Web3 Project
        </Button>
      </div>
      {/* ... Add more sections as needed ... */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: "#0a1433" }}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-6 fade-in-up">
              Ready to Build Your Web3 Startup?
            </h2>
            <p className="text-xl text-muted-foreground fade-in-up" style={{ animationDelay: "0.2s" }}>
              Let's create the next big thing in blockchain, DeFi, and decentralized apps.
            </p>
          </div>
          <div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
            <ContactForm pageSource="Web3 Startup Page" businessTypes={['Web3 Founder','Blockchain Developer','Crypto Marketer','Other']} />
          </div>
        </div>
      </section>
    </div>
  );
};

export default Web3Startup;
