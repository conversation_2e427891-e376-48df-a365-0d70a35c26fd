
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Menu, X, Sparkles } from "lucide-react";

interface NavBarProps {
  currentPage?: string;
  showBackButton?: boolean;
}

const NavBar = ({ currentPage, showBackButton = false }: NavBarProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const scrollToSection = (sectionId: string) => {
    if (currentPage && currentPage !== 'home') {
      // If we're on a niche page, go to home first then scroll
      navigate(`/#${sectionId}`);
    } else {
      // If we're on home page, just scroll
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
    setIsMenuOpen(false);
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  return (
    <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <Sparkles className="h-8 w-8 text-accent" />
            <span className="text-2xl font-bold text-foreground">
              IssyLabs
              {currentPage && currentPage !== 'home' && (
                <span className="text-accent ml-2">× {currentPage}</span>
              )}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {showBackButton && (
              <button 
                onClick={handleBackToHome}
                className="text-muted-foreground hover:text-accent transition-colors font-medium"
              >
                ← Back to Home
              </button>
            )}
            <button 
              onClick={() => scrollToSection('home')}
              className="text-muted-foreground hover:text-accent transition-colors font-medium"
            >
              Home
            </button>
            <button 
              onClick={() => scrollToSection('about')}
              className="text-muted-foreground hover:text-accent transition-colors font-medium"
            >
              About
            </button>
            <button 
              onClick={() => scrollToSection('services')}
              className="text-muted-foreground hover:text-accent transition-colors font-medium"
            >
              Services
            </button>
            <button 
              onClick={() => scrollToSection('portfolio')}
              className="text-muted-foreground hover:text-accent transition-colors font-medium"
            >
              Portfolio
            </button>
            <button 
              onClick={() => scrollToSection('testimonials')}
              className="text-muted-foreground hover:text-accent transition-colors font-medium"
            >
              Testimonials
            </button>
            <button 
              onClick={() => scrollToSection('contact')}
              className="text-muted-foreground hover:text-accent transition-colors font-medium"
            >
              Contact
            </button>
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <Button 
              className="bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift"
              onClick={() => scrollToSection('contact')}
            >
              Start Project
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6 text-foreground" />
            ) : (
              <Menu className="h-6 w-6 text-foreground" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 space-y-4">
            {showBackButton && (
              <button 
                onClick={handleBackToHome}
                className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
              >
                ← Back to Home
              </button>
            )}
            <button 
              onClick={() => scrollToSection('home')}
              className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
            >
              Home
            </button>
            <button 
              onClick={() => scrollToSection('about')}
              className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
            >
              About
            </button>
            <button 
              onClick={() => scrollToSection('services')}
              className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
            >
              Services
            </button>
            <button 
              onClick={() => scrollToSection('portfolio')}
              className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
            >
              Portfolio
            </button>
            <button 
              onClick={() => scrollToSection('testimonials')}
              className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
            >
              Testimonials
            </button>
            <button 
              onClick={() => scrollToSection('contact')}
              className="block w-full text-left text-muted-foreground hover:text-accent transition-colors font-medium py-2"
            >
              Contact
            </button>
            <Button 
              className="w-full bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg"
              onClick={() => scrollToSection('contact')}
            >
              Start Project
            </Button>
          </div>
        )}
      </div>
    </nav>
  );
};

export default NavBar;
